import request from '/@/utils/request'
// 查询模型信息列表
export function listModulesInfo(query:object) {
  return request({
    url: '/api/v1/system/modulesInfo/list',
    method: 'get',
    params: query
  })
}

export function ListModulesInfoField(query:object) {
  return request({
    url: '/api/v1/system/modulesInfoField/list',
    method: 'get',
    params: query
  })
}

// 查询模型信息详细
export function getModulesInfo(id:number) {
  return request({
    url: '/api/v1/system/modulesInfo/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增模型信息
export function addModulesInfo(data:object) {
  return request({
    url: '/api/v1/system/modulesInfo/add',
    method: 'post',
    data: data
  })
}
// 修改模型信息
export function updateModulesInfo(data:object) {
  return request({
    url: '/api/v1/system/modulesInfo/edit',
    method: 'put',
    data: data
  })
}
// 删除模型信息
export function delModulesInfo(ids:number[]) {
  return request({
    url: '/api/v1/system/modulesInfo/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}

// 生成模型
export function createModule(id:number){
  return request({
    url: '/api/v1/system/modulesInfo/createModule',
    method: 'put',
    data: {id}
  })
}
