import request from '/@/utils/request'
// 查询文章评论列表
export function listCmsComment(query:object) {
  return request({
    url: '/api/v1/cms/cmsArticleComment/list',
    method: 'get',
    params: query
  })
}
// 查询文章评论详细
export function getCmsComment(id:number) {
  return request({
    url: '/api/v1/cms/cmsArticleComment/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 修改文章评论
export function updateCmsComment(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticleComment/edit',
    method: 'put',
    data: data
  })
}
// 删除文章评论
export function delCmsComment(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsArticleComment/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}

// 文章评论状态修改
export function changeCmsCommentStatus(id:number,status:number) {
  const data = {
    id,
    status
  }
  return request({
    url: '/api/v1/cms/cmsArticleComment/changeStatus',
    method: 'put',
    data:data
  })
}

// 删除评论回复
export function delCmsCommentReply(id:number) {
  return request({
    url: '/api/v1/cms/cmsArticleComment/delete',
    method: 'delete',
    data:{
      ids:[id]
    }
  })
}

// 文章评论回复状态修改
export function changeCmsCommentReplyStatus(id:number,status:number) {
  const data = {
    id,
    status
  }
  return request({
    url: '/api/v1/cms/cmsArticleComment/changeStatus',
    method: 'put',
    data:data
  })
}
