import request from '/@/utils/request'
// 查询广告位置列表
export function listCmsAdPosition(query:object) {
  return request({
    url: '/api/v1/cms/cmsAdPosition/list',
    method: 'get',
    params: query
  })
}
// 查询广告位置详细
export function getCmsAdPosition(id:number) {
  return request({
    url: '/api/v1/cms/cmsAdPosition/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增广告位置
export function addCmsAdPosition(data:object) {
  return request({
    url: '/api/v1/cms/cmsAdPosition/add',
    method: 'post',
    data: data
  })
}
// 修改广告位置
export function updateCmsAdPosition(data:object) {
  return request({
    url: '/api/v1/cms/cmsAdPosition/edit',
    method: 'put',
    data: data
  })
}
// 删除广告位置
export function delCmsAdPosition(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsAdPosition/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
