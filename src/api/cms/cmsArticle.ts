import request from '/@/utils/request'
// 查询文章列表
export function listCmsArticle(query:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/list',
    method: 'get',
    params: query
  })
}
// 查询文章详细
export function getCmsArticle(id:number) {
  return request({
    url: '/api/v1/cms/cmsArticle/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增文章
export function addCmsArticle(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/add',
    method: 'post',
    data: data
  })
}
// 修改文章
export function updateCmsArticle(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/edit',
    method: 'put',
    data: data
  })
}
// 删除文章
export function delCmsArticle(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsArticle/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}

// 推送文章
export function pushCmsArticle(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/push',
    method: 'put',
    data: data
  })
}

// 移动文章
export function moveCmsArticle(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/move',
    method: 'put',
    data: data
  })
}

//发布文章
export function publishCmsArticle(data:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/publish',
    method: 'put',
    data: data
  })
}
// 回收站
export function recycleCmsArticle(query:object) {
  return request({
    url: '/api/v1/cms/cmsArticle/recycle',
    method: 'get',
    params: query
  })
}

// 回收站恢复文章
export function recycleRevertCmsArticle(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsArticle/recycle/revert',
    method: 'put',
    data:{
      ids:ids
    }
  })
}

// 回收站删除文章
export function recycleDelCmsArticle(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsArticle/recycle/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}

