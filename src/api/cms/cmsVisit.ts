import request from '/@/utils/request'

export function ListCmsVisit(query:object) {
  return request({
    url: '/api/v1/cms/cmsVisit/list',
    method: 'get',
    params: query
  })
}

export function CategoryCmsVist(query:object) {
  return request({
    url: '/api/v1/cms/cmsVisit/category',
    method: 'get',
    params: query
  })
}

export function ArticleCmsVist(query:object) {
  return request({
    url: '/api/v1/cms/cmsVisit/article',
    method: 'get',
    params: query
  })
}