import request from '/@/utils/request'
// 查询用户中心配置列表
export function listUcenterConfig(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterConfig/list',
    method: 'get',
    params: query
  })
}
// 查询用户中心配置详细
export function getUcenterConfig(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterConfig/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户中心配置
export function addUcenterConfig(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterConfig/add',
    method: 'post',
    data: data
  })
}
// 修改用户中心配置
export function updateUcenterConfig(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterConfig/edit',
    method: 'put',
    data: data
  })
}
// 删除用户中心配置
export function delUcenterConfig(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterConfig/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
