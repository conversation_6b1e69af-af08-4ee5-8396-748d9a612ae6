import request from '/@/utils/request'
// 查询用户消息列表
export function listUcenterMessage(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMessage/list',
    method: 'get',
    params: query
  })
}
// 查询用户消息详细
export function getUcenterMessage(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMessage/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户消息
export function addUcenterMessage(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMessage/add',
    method: 'post',
    data: data
  })
}
// 修改用户消息
export function updateUcenterMessage(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMessage/edit',
    method: 'put',
    data: data
  })
}
// 删除用户消息
export function delUcenterMessage(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMessage/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
