import request from '/@/utils/request'
// 查询系统访问记录列表
export function listUcenterLoginLog(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLoginLog/list',
    method: 'get',
    params: query
  })
}
// 查询系统访问记录详细
export function getUcenterLoginLog(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLoginLog/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增系统访问记录
export function addUcenterLoginLog(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLoginLog/add',
    method: 'post',
    data: data
  })
}
// 修改系统访问记录
export function updateUcenterLoginLog(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLoginLog/edit',
    method: 'put',
    data: data
  })
}
// 删除系统访问记录
export function delUcenterLoginLog(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLoginLog/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
