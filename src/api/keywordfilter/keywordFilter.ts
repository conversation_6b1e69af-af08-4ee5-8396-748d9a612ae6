import request from '/@/utils/request'
// 查询敏感词过滤列表
export function listKeywordFilter(query:object) {
  return request({
    url: '/api/v1/keywordfilter/keywordFilter/list',
    method: 'get',
    params: query
  })
}
// 查询敏感词过滤详细
export function getKeywordFilter(id:number) {
  return request({
    url: '/api/v1/keywordfilter/keywordFilter/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增敏感词过滤
export function addKeywordFilter(data:object) {
  return request({
    url: '/api/v1/keywordfilter/keywordFilter/add',
    method: 'post',
    data: data
  })
}
// 修改敏感词过滤
export function updateKeywordFilter(data:object) {
  return request({
    url: '/api/v1/keywordfilter/keywordFilter/edit',
    method: 'put',
    data: data
  })
}
// 删除敏感词过滤
export function delKeywordFilter(ids:number[]) {
  return request({
    url: '/api/v1/keywordfilter/keywordFilter/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
