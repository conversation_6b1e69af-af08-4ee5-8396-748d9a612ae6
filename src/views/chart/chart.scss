.chart-scrollbar {
	.chart-warp {
		display: flex;
		flex-direction: column;
		height: 100%;
		.chart-warp-bottom {
			flex: 1;
			overflow: hidden;
			display: flex;
			.big-data-down-left,
			.big-data-down-right {
				width: 30%;
				display: flex;
				flex-direction: column;
				.flex-warp-item {
					padding: 0 7.5px 15px 15px;
					width: 100%;
					height: 33.33%;
					.flex-warp-item-box {
						width: 100%;
						height: 100%;
						background: var(--el-color-white);
						border: 1px solid var(--el-border-color-lighter);
						border-radius: 4px;
						display: flex;
						flex-direction: column;
						padding: 15px;
						transition: all ease 0.3s;
						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}
						.flex-title {
							margin-bottom: 15px;
							display: flex;
							justify-content: space-between;
							.flex-title-small {
								font-size: 12px;
							}
						}
						.flex-content {
							flex: 1;
							font-size: 12px;
						}
						.flex-content-overflow {
							overflow: hidden;
						}
					}
				}
			}
			.big-data-down-left {
				color: var(--el-text-color-primary);
				.sky {
					display: flex;
					align-items: center;
					.sky-left {
						font-size: 30px;
					}
					.sky-center {
						flex: 1;
						overflow: hidden;
						padding: 0 10px;
						font {
							margin-right: 15px;
						}
						.span {
							background: #22bc76;
							border-radius: 2px;
							padding: 0 5px;
							color: var(--el-color-white);
						}
					}
					.sky-right {
						span {
							font-size: 30px;
						}
						font {
							font-size: 20px;
						}
					}
				}
				.sky-dd {
					.sky-dl {
						display: flex;
						align-items: center;
						height: 28px;
						overflow: hidden;
						div {
							flex: 1;
							overflow: hidden;
							i {
								font-size: 14px;
							}
						}
						.tip {
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
					.sky-dl-first {
						color: var(--el-color-primary);
					}
				}
				.d-states {
					display: flex;
					.d-states-item {
						flex: 1;
						display: flex;
						align-items: center;
						overflow: hidden;
						i {
							font-size: 20px;
							height: 33px;
							width: 33px;
							line-height: 33px;
							text-align: center;
							border-radius: 100%;
							flex-shrink: 1;
							color: var(--el-color-white);
							display: flex;
							align-items: center;
							justify-content: center;
						}
						.i-bg1 {
							background: #22bc76;
						}
						.i-bg2 {
							background: #e2356d;
						}
						.i-bg3 {
							background: #43bbef;
						}
						.d-states-flex {
							overflow: hidden;
							padding: 0 10px 0;
							.d-states-item-label {
								color: var(--el-color-primary);
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}
							.d-states-item-value {
								font-size: 14px;
								text-align: center;
								margin-top: 3px;
								color: var(--el-color-primary);
							}
						}
					}
				}
				.d-btn {
					margin-top: 5px;
					.d-btn-item {
						border: 1px solid var(--el-color-primary);
						display: flex;
						width: 100%;
						border-radius: 35px;
						align-items: center;
						padding: 5px;
						margin-top: 15px;
						cursor: pointer;
						transition: all ease 0.3s;
						color: var(--el-color-primary);
						.d-btn-item-left {
							font-size: 20px;
							border: 1px solid var(--el-color-primary);
							width: 25px;
							height: 25px;
							line-height: 25px;
							border-radius: 100%;
							text-align: center;
							font-size: 14px;
						}
						.d-btn-item-center {
							padding: 0 10px;
							flex: 1;
						}
						.d-btn-item-eight {
							text-align: right;
							padding-right: 10px;
						}
					}
				}
			}
			.big-data-down-center {
				width: 40%;
				display: flex;
				flex-direction: column;
				.big-data-down-center-one {
					height: 66.67%;
					padding: 0 7.5px 15px;
					.big-data-down-center-one-content {
						height: 100%;
						background: var(--el-color-white);
						padding: 15px;
						border: 1px solid var(--el-border-color-lighter);
						border-radius: 4px;
						transition: all ease 0.3s;
						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}
					}
				}
				.big-data-down-center-two {
					padding: 0 7.5px 15px;
					height: 33.33%;
					.flex-warp-item-box {
						width: 100%;
						height: 100%;
						background: var(--el-color-white);
						display: flex;
						flex-direction: column;
						padding: 15px;
						border: 1px solid var(--el-border-color-lighter);
						border-radius: 4px;
						transition: all ease 0.3s;
						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}
						.flex-title {
							margin-bottom: 15px;
							color: var(--el-text-color-primary);
							display: flex;
							justify-content: space-between;
							.flex-title-small {
								font-size: 12px;
							}
						}
						.flex-content {
							flex: 1;
							font-size: 12px;
							display: flex;
							height: calc(100% - 30px);
							.flex-content-left {
								display: flex;
								flex-wrap: wrap;
								width: 120px;
								height: 100%;
								.monitor-item {
									width: 50%;
									display: flex;
									align-items: center;
									.monitor-wave {
										cursor: pointer;
										width: 40px;
										height: 40px;
										position: relative;
										background-color: var(--el-color-primary);
										border-radius: 50%;
										overflow: hidden;
										text-align: center;
										&::before,
										&::after {
											content: '';
											position: absolute;
											left: 50%;
											width: 40px;
											height: 40px;
											background: #f4f4f4;
											animation: roateOne 10s linear infinite;
											transform: translateX(-50%);
											z-index: 1;
										}
										&::before {
											bottom: 10px;
											border-radius: 60%;
										}
										&::after {
											bottom: 8px;
											opacity: 0.7;
											border-radius: 37%;
										}
										.monitor-z-index {
											position: relative;
											z-index: 2;
											color: var(--el-color-primary);
											display: flex;
											align-items: center;
											height: 100%;
											justify-content: center;
										}
									}
									@keyframes roateOne {
										0% {
											transform: translate(-50%, 0) rotateZ(0deg);
										}
										50% {
											transform: translate(-50%, -2%) rotateZ(180deg);
										}
										100% {
											transform: translate(-50%, 0%) rotateZ(360deg);
										}
									}
									.monitor-active {
										background-color: #22bc76;
										.monitor-z-index {
											color: #22bc76;
										}
									}
								}
							}
							.flex-content-right {
								flex: 1;
							}
						}
					}
				}
			}
			.big-data-down-right {
				.flex-warp-item {
					padding: 0 15px 15px 7.5px;
					.flex-title {
						color: var(--el-text-color-primary);
					}
					.flex-content {
						display: flex;
						flex-direction: column;
						.task {
							display: flex;
							height: 45px;
							.task-item {
								flex: 1;
								color: var(--el-color-white);
								display: flex;
								justify-content: center;
								.task-item-box {
									position: relative;
									width: 45px;
									height: 45px;
									overflow: hidden;
									border-radius: 100%;
									z-index: 0;
									display: flex;
									align-items: center;
									flex-direction: column;
									justify-content: center;
									box-shadow: 0 10px 12px 0 rgba(0, 0, 0, 0.3);
									&::before {
										content: '';
										position: absolute;
										z-index: -2;
										left: -50%;
										top: -50%;
										width: 200%;
										height: 200%;
										background-repeat: no-repeat;
										background-size: 50% 50%, 50% 50%;
										background-position: 0 0, 100% 0, 100% 100%, 0 100%;
										background-image: linear-gradient(#19d4ae, #19d4ae), linear-gradient(#5ab1ef, #5ab1ef), linear-gradient(#fa6e86, #fa6e86),
											linear-gradient(#ffb980, #ffb980);
										animation: rotate 2s linear infinite;
									}
									&::after {
										content: '';
										position: absolute;
										z-index: -1;
										left: 1px;
										top: 1px;
										width: calc(100% - 2px);
										height: calc(100% - 2px);
										border-radius: 100%;
									}
									.task-item-value {
										text-align: center;
										font-size: 14px;
										font-weight: bold;
									}
									.task-item-label {
										text-align: center;
									}
								}
								.task1 {
									&::after {
										background: #5492be;
									}
								}
								.task2 {
									&::after {
										background: #43a177;
									}
								}
								.task3 {
									&::after {
										background: #a76077;
									}
								}
							}
							.task-first-item {
								flex-direction: column;
								text-align: center;
								color: var(--el-color-primary);
								.task-first {
									font-size: 20px;
								}
							}
						}
						.progress {
							color: var(--el-text-color-primary);
							display: flex;
							flex-direction: column;
							flex: 1;
							justify-content: space-between;
							margin-top: 15px;
							.progress-item {
								height: 33.33%;
								display: flex;
								align-items: center;
								.progress-box {
									flex: 1;
									width: 100%;
									margin-left: 10px;
									:deep(.el-progress__text) {
										color: var(--el-text-color-primary);
										font-size: 12px !important;
										text-align: right;
									}
									:deep(.el-progress-bar__outer) {
										background-color: rgba(0, 0, 0, 0.1) !important;
									}
									:deep(.el-progress-bar) {
										margin-right: -22px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
