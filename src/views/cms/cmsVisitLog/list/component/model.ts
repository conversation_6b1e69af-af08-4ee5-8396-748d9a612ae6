export interface CmsVisitLogTableColumns {    
    id:number;  // ID    
    ip:string;  // 访问IP    
    url:string;  // URL    
    agent:string;  // AGENT    
    articleId:number;  // 文章id    
    categoryId:number;  // 分类id    
    createdAt:string;  // 访问时间    
}


export interface CmsVisitLogInfoData {    
    id:number|undefined;        // ID    
    ip:string|undefined; // 访问IP    
    url:string|undefined; // URL    
    agent:string|undefined; // AGENT    
    articleId:number|undefined; // 文章id    
    categoryId:number|undefined; // 分类id    
    createdAt:string|undefined; // 访问时间    
}


export interface CmsVisitLogTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsVisitLogTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            ip: string|undefined;            
            url: string|undefined;            
            agent: string|undefined;            
            articleId: number|undefined;            
            categoryId: number|undefined;            
            createdAt: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsVisitLogEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsVisitLogInfoData;
    rules: object;
}