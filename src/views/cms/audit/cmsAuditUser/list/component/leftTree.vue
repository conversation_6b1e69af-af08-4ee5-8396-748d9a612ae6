<template>
  <div class="left-tree">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="角色用户" name="role">
        <div class="tree-header">
          <div class="search-box">
            <el-input
              v-model="filterText"
              placeholder="输入关键字搜索"
              clearable
              class="tree-filter"
              @keyup.enter="handleTreeSearch"
            >
              <template #append>
                <el-button @click="handleTreeSearch">
                  <el-icon><ele-Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="tree-buttons">
            <el-button link @click="refreshTree">
              <el-icon><ele-Refresh /></el-icon>
              刷新
            </el-button>
            <el-button link @click="toggleExpand">
              <el-icon><ele-ArrowDown v-if="isExpanded" /><ele-ArrowRight v-else /></el-icon>
              {{ isExpanded ? '收缩' : '展开' }}
            </el-button>
          </div>
        </div>
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="{
            label: 'name',
            children: 'children'
          }"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          @node-expand="handleNodeExpand"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          :default-expand-all="false"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <!-- 如果是用户节点 -->
              <template v-if="data.isUser">
                <div class="user-node">
                  <el-avatar :size="24" :src="data.avatar || ''">{{ data.name?.substring(0, 1) }}</el-avatar>
                  <span class="user-name">{{ data.name }}</span>
                </div>
              </template>
              <!-- 如果是角色节点 -->
              <template v-else>
                <span>{{ node.label }}</span>
              </template>
            </div>
          </template>
        </el-tree>
      </el-tab-pane>

      <el-tab-pane label="部门用户" name="dept">
        <div class="tree-header">
          <div class="search-box">
            <el-input
              v-model="deptFilterText"
              placeholder="输入关键字搜索"
              clearable
              class="tree-filter"
              @keyup.enter="handleDeptTreeSearch"
            >
              <template #append>
                <el-button @click="handleDeptTreeSearch">
                  <el-icon><ele-Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="tree-buttons">
            <el-button link @click="refreshDeptTree">
              <el-icon><ele-Refresh /></el-icon>
              刷新
            </el-button>
            <el-button link @click="toggleDeptExpand">
              <el-icon><ele-ArrowDown v-if="isDeptExpanded" /><ele-ArrowRight v-else /></el-icon>
              {{ isDeptExpanded ? '收缩' : '展开' }}
            </el-button>
          </div>
        </div>
        <el-tree
          ref="deptTreeRef"
          :data="deptTreeData"
          :props="{
            label: 'deptName',
            children: 'children'
          }"
          node-key="deptId"
          highlight-current
          @node-click="handleDeptNodeClick"
          @node-expand="handleDeptNodeExpand"
          :expand-on-click-node="false"
          :filter-node-method="filterDeptNode"
          :default-expand-all="false"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <!-- 如果是用户节点 -->
              <template v-if="data.isUser">
                <div class="user-node">
                  <el-avatar :size="24" :src="data.avatar || ''">{{ data.name?.substring(0, 1) }}</el-avatar>
                  <span class="user-name">{{ data.name }}</span>
                </div>
              </template>
              <!-- 如果是部门节点 -->
              <template v-else>
                <span>{{ node.label }}</span>
              </template>
            </div>
          </template>
        </el-tree>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, getCurrentInstance } from "vue";
import { ElMessage } from "element-plus";
import { getRoleList } from "/@/api/system/role";
import { getUserList } from "/@/api/system/user";
import { getDeptList } from "/@/api/system/dept";
import { Search, Refresh, ArrowDown, ArrowRight } from '@element-plus/icons-vue';

interface TreeNodeData {
  id: number;
  name: string;
  pid: number;
  status: number;
  userCnt: number;
  children?: TreeNodeData[];
  isLoaded?: boolean; // 标记是否已加载过用户列表
  users?: any[]; // 存储该角色的用户列表
}

interface DeptTreeNodeData {
  deptId: number;
  deptName: string;
  parentId: number;
  status: number;
  children?: DeptTreeNodeData[];
  isLoaded?: boolean; // 标记是否已加载过用户列表
  users?: any[]; // 存储该部门的用户列表
}

export default defineComponent({
  name: "LeftTree",
  components: {
    'ele-Search': Search,
    'ele-Refresh': Refresh,
    'ele-ArrowDown': ArrowDown,
    'ele-ArrowRight': ArrowRight
  },
  emits: ["node-click", "user-node-click"],
  setup(props, { emit }) {
    const { proxy } = <any>getCurrentInstance();

    // Tab 相关
    const activeTab = ref('role'); // 默认显示角色用户 tab

    // 角色树相关
    const filterText = ref('');
    const treeRef = ref();
    const treeData = ref<TreeNodeData[]>([]);
    const currentNode = ref<TreeNodeData | null>(null);
    const isExpanded = ref(false);

    // 部门树相关
    const deptFilterText = ref('');
    const deptTreeRef = ref();
    const deptTreeData = ref<DeptTreeNodeData[]>([]);
    const currentDeptNode = ref<DeptTreeNodeData | null>(null);
    const isDeptExpanded = ref(false);

    // 监听搜索关键字变化
    watch(filterText, (val) => {
      treeRef.value?.filter(val);
    });

    // 监听部门搜索关键字变化
    watch(deptFilterText, (val) => {
      deptTreeRef.value?.filter(val);
    });

    // 树节点过滤方法
    const filterNode = (value: string, data: any) => {
      if (!value) return true;
      return data.name.toLowerCase().includes(value.toLowerCase());
    };

    // 部门树节点过滤方法
    const filterDeptNode = (value: string, data: any) => {
      if (!value) return true;
      if (data.isUser) {
        return data.name.toLowerCase().includes(value.toLowerCase());
      }
      return data.deptName.toLowerCase().includes(value.toLowerCase());
    };

    // 处理树搜索
    const handleTreeSearch = () => {
      treeRef.value?.filter(filterText.value);
    };

    // 处理部门树搜索
    const handleDeptTreeSearch = () => {
      deptTreeRef.value?.filter(deptFilterText.value);
    };

    // 处理标签页切换
    const handleTabClick = (tab: any) => {
      if (tab.props.name === 'dept' && deptTreeData.value.length === 0) {
        // 如果切换到部门标签页且部门树数据为空，则初始化部门树数据
        initDeptTreeData();
      }
    };

    // 初始化角色树数据
    const initTreeData = async () => {
      try {
        // 显示加载中提示
        const loading = ElMessage.info({
          message: '正在加载角色数据...',
          duration: 0
        });

        // 并行获取角色列表和所有用户数据
        const [roleRes, userRes] = await Promise.all([
          getRoleList({}),
          getUserList({ pageNum: 1, pageSize: 10000 }) // 获取所有用户
        ]);

        // 处理角色数据
        let roleList = roleRes.data.list ?? [];

        // 使用proxy.handleTree处理树形结构
        const roleTree = proxy.handleTree(roleList.map((item: any) => ({
          id: item.id,
          name: item.name,
          pid: item.pid,
          status: item.status,
          userCnt: item.userCnt,
          dataScope: item.dataScope,
          isLoaded: true // 预先标记为已加载
        })), "id", "pid");

        // 处理用户数据
        const allUsers = userRes.data.userList ?? [];

        // 获取每个用户的角色列表
        // 注意：这里需要先获取每个用户的角色关系
        // 由于用户和角色是多对多关系，需要先获取所有角色的用户列表

        // 创建一个存储所有角色的用户映射
        const usersByRole = {};

        // 对每个角色，获取其用户列表
        const fetchUsersForRoles = async () => {
          for (const role of roleList) {
            try {
              const res = await getUserList({
                pageNum: 1,
                pageSize: 1000,
                roleId: role.id
              });

              if (res.code === 0 && res.data && res.data.userList) {
                usersByRole[role.id] = res.data.userList;
              }
            } catch (error) {
              console.error(`获取角色 ${role.id} 的用户列表失败`, error);
            }
          }
        };

        // 获取所有角色的用户
        await fetchUsersForRoles();

        // 将用户数据添加到角色树中
        const addUsersToRoleTree = (nodes) => {
          if (!nodes || nodes.length === 0) return;

          for (const node of nodes) {
            const roleId = node.id;

            // 如果该角色有用户，将用户添加为子节点
            if (usersByRole[roleId] && usersByRole[roleId].length > 0) {
              const userNodes = usersByRole[roleId].map(user => ({
                id: `user_${user.id}`,
                name: user.userNickname || user.userName,
                isUser: true,
                avatar: user.avatar,
                originalData: user
              }));

              // 如果有子节点，保存原有的子角色节点
              const originalChildren = node.children || [];

              // 过滤掉原有的用户节点，只保留角色节点
              const roleChildren = originalChildren.filter(child => !child.isUser);

              // 创建新的子节点数组，先添加用户节点，再添加角色节点
              node.children = [...userNodes, ...roleChildren];
            }

            // 递归处理子角色
            if (node.children) {
              // 过滤出角色节点（非用户节点）
              const roleChildren = node.children.filter(child => !child.isUser);
              if (roleChildren.length > 0) {
                addUsersToRoleTree(roleChildren);
              }
            }
          }
        };

        // 将用户添加到角色树中
        addUsersToRoleTree(roleTree);

        // 设置角色树数据
        treeData.value = roleTree;

        // 关闭加载提示
        loading.close();
        ElMessage.success('角色用户数据加载完成');
      } catch (error) {
        console.error('加载角色和用户数据失败', error);
        ElMessage.error("获取角色列表失败");
      }
    };

    // 初始化部门树数据
    const initDeptTreeData = async () => {
      try {
        // 显示加载中提示
        const loading = ElMessage.info({
          message: '正在加载数据...',
          duration: 0
        });

        // 并行获取部门列表和所有用户数据
        const [deptRes, userRes] = await Promise.all([
          getDeptList({}),
          getUserList({ pageNum: 1, pageSize: 10000 }) // 获取所有用户
        ]);

        // 处理部门数据
        let deptList = deptRes.data.deptList ?? [];

        // 使用proxy.handleTree处理树形结构
        const deptTree = proxy.handleTree(deptList.map((item: any) => ({
          deptId: item.deptId,
          deptName: item.deptName,
          parentId: item.parentId,
          status: item.status,
          orderNum: item.orderNum,
          isLoaded: true // 预先标记为已加载
        })), "deptId", "parentId");

        // 处理用户数据
        const allUsers = userRes.data.userList ?? [];

        // 按部门ID分组用户
        const usersByDept = {};
        allUsers.forEach(user => {
          if (user.deptId) {
            if (!usersByDept[user.deptId]) {
              usersByDept[user.deptId] = [];
            }
            usersByDept[user.deptId].push(user);
          }
        });

        // 将用户数据添加到部门树中
        const addUsersToDeptTree = (nodes) => {
          if (!nodes || nodes.length === 0) return;

          for (const node of nodes) {
            const deptId = node.deptId;

            // 如果该部门有用户，将用户添加为子节点
            if (usersByDept[deptId] && usersByDept[deptId].length > 0) {
              const userNodes = usersByDept[deptId].map(user => ({
                deptId: `user_${user.id}`,
                deptName: user.userNickname || user.userName,
                name: user.userNickname || user.userName,
                isUser: true,
                avatar: user.avatar,
                originalData: user
              }));

              // 如果有子节点，保存原有的子部门节点
              const originalChildren = node.children || [];

              // 创建新的子节点数组，先添加用户节点，再添加部门节点
              node.children = [...userNodes, ...originalChildren];
            }

            // 递归处理子部门
            if (node.children) {
              // 过滤出部门节点（非用户节点）
              const deptChildren = node.children.filter(child => !child.isUser);
              if (deptChildren.length > 0) {
                addUsersToDeptTree(deptChildren);
              }
            }
          }
        };

        // 将用户添加到部门树中
        addUsersToDeptTree(deptTree);

        // 设置部门树数据
        deptTreeData.value = deptTree;

        // 关闭加载提示
        loading.close();
        ElMessage.success('部门用户数据加载完成');
      } catch (error) {
        console.error('加载部门和用户数据失败', error);
        ElMessage.error("获取数据失败");
      }
    };





    // 处理角色节点展开事件
    const handleNodeExpand = (data: any, node: any) => {
      console.log('展开角色节点:', data, node);
      // 所有用户数据已预加载，不需要在展开时再次加载
    };

    // 处理部门节点展开事件
    const handleDeptNodeExpand = (data: any, node: any) => {
      console.log('展开部门节点:', data, node);
      // 所有用户数据已预加载，不需要在展开时再次加载
    };

    // 添加角色节点点击处理
    const handleNodeClick = (data: any, node: any) => {
      console.log('点击角色节点:', data, node);

      // 如果是用户节点，发送用户ID
      if (data.isUser) {
        // 从用户节点ID中提取原始用户ID
        // 用户节点ID格式为 "user_123"，需要提取数字部分
        const userId = data.originalData?.id || parseInt(data.id.toString().replace('user_', ''));
        console.log('点击用户节点，用户ID:', userId);
        // 触发用户节点点击事件
        emit('user-node-click', userId);
        return;
      }

      // 更新当前选中节点
      currentNode.value = data;

      // 切换节点的展开/收起状态
      if (node.expanded) {
        node.collapse();
      } else {
        node.expand();
        // 所有用户数据已预加载，不需要在点击时再次加载
      }

      // 触发选中事件
      emit('node-click', data);
    };

    // 添加部门节点点击处理
    const handleDeptNodeClick = (data: any, node: any) => {
      console.log('点击部门节点:', data, node);

      // 如果是用户节点，发送用户ID
      if (data.isUser) {
        // 从用户节点ID中提取原始用户ID
        // 用户节点ID格式为 "user_123"，需要提取数字部分
        let userId;
        if (data.originalData && data.originalData.id) {
          userId = data.originalData.id;
        } else {
          userId = parseInt(data.deptId.toString().replace('user_', ''));
        }
        console.log('点击用户节点，用户ID:', userId);
        // 触发用户节点点击事件
        emit('user-node-click', userId);
        return;
      }

      // 更新当前选中节点
      currentDeptNode.value = data;

      // 切换节点的展开/收起状态
      if (node.expanded) {
        node.collapse();
      } else {
        node.expand();
        // 所有用户数据已预加载，不需要在点击时再次加载
      }

      // 触发选中事件
      emit('node-click', data);
    };

    // 添加刷新角色树的方法
    const refreshTree = async () => {
      try {
        // 直接调用初始化方法重新加载数据
        await initTreeData();

        // 如果当前有选中节点，需要重新选中
        if (currentNode.value) {
          const node = treeRef.value.getNode(currentNode.value.id);
          if (node) {
            treeRef.value.setCurrentKey(currentNode.value.id);
          } else {
            currentNode.value = null;
          }
        }
      } catch (error) {
        console.error(error);
        ElMessage.error("刷新角色列表失败");
      }
    };

    // 添加刷新部门树的方法
    const refreshDeptTree = async () => {
      try {
        // 直接调用初始化方法重新加载数据
        await initDeptTreeData();

        // 如果当前有选中节点，需要重新选中
        if (currentDeptNode.value) {
          const node = deptTreeRef.value.getNode(currentDeptNode.value.deptId);
          if (node) {
            deptTreeRef.value.setCurrentKey(currentDeptNode.value.deptId);
          } else {
            currentDeptNode.value = null;
          }
        }
      } catch (error) {
        console.error(error);
        ElMessage.error("刷新部门列表失败");
      }
    };

    // 展开/收缩所有角色节点
    const toggleExpand = () => {
      if (!treeRef.value) return;

      // 获取所有节点
      const nodes = treeRef.value.store.nodesMap;

      // 切换展开状态
      isExpanded.value = !isExpanded.value;

      // 遍历所有节点进行展开或收缩操作
      for (const nodeKey in nodes) {
        if (nodes[nodeKey].childNodes.length > 0) {
          isExpanded.value ? nodes[nodeKey].expand() : nodes[nodeKey].collapse();
        }
      }
    };

    // 展开/收缩所有部门节点
    const toggleDeptExpand = () => {
      if (!deptTreeRef.value) return;

      // 获取所有节点
      const nodes = deptTreeRef.value.store.nodesMap;

      // 切换展开状态
      isDeptExpanded.value = !isDeptExpanded.value;

      // 遍历所有节点进行展开或收缩操作
      for (const nodeKey in nodes) {
        if (nodes[nodeKey].childNodes.length > 0) {
          isDeptExpanded.value ? nodes[nodeKey].expand() : nodes[nodeKey].collapse();
        }
      }
    };

    onMounted(() => {
      initTreeData();
      // 同时初始化部门树数据，这样当用户切换到部门用户标签页时，数据已经准备好了
      initDeptTreeData();
    });

    return {
      // Tab 相关
      activeTab,
      handleTabClick,

      // 角色树相关
      filterText,
      treeRef,
      treeData,
      isExpanded,
      handleTreeSearch,
      filterNode,
      handleNodeClick,
      refreshTree,
      toggleExpand,
      handleNodeExpand,

      // 部门树相关
      deptFilterText,
      deptTreeRef,
      deptTreeData,
      isDeptExpanded,
      handleDeptTreeSearch,
      filterDeptNode,
      handleDeptNodeClick,
      refreshDeptTree,
      toggleDeptExpand,
      handleDeptNodeExpand,
    };
  }
});
</script>

<style lang="scss" scoped>
.left-tree {
  width: 250px;
  padding: 10px;
  border-right: 1px solid #dcdfe6;
  overflow: auto;
  height: 100%;

  :deep(.el-tabs__content) {
    overflow: visible;
  }

  :deep(.el-tabs__nav) {
    width: 100%;
    display: flex;
  }

  :deep(.el-tabs__item) {
    flex: 1;
    text-align: center;
  }

  .tree-header {
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;

    .search-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tree-filter {
        flex: 1;
      }
    }

    .tree-buttons {
      display: flex;
      justify-content: space-between;

      .el-button {
        padding: 4px 0;

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .role-info {
    display: flex;
    align-items: center;

    .user-count {
      margin-left: 5px;
      font-size: 12px;
      color: #909399;
    }
  }

  .user-node {
    display: flex;
    align-items: center;
    padding: 2px 0;
  }

  .user-name {
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
  }
}
</style>
