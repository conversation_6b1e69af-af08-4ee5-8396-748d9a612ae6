export interface CmsAuditUserTableColumns {
    id:number;  // ID
    userId:number;  // 管理员ID
    step:number;  // 第几审
    stepId:number;  // 步骤ID
}


export interface CmsAuditUserInfoData {
    id:number|undefined;        // ID
    userId:number|undefined; // 管理员ID
    step:number|undefined; // 第几审
    stepId:number|undefined; // 步骤ID
}


export interface CmsAuditUserTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAuditUserTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            userId: number|undefined;
        };
    };
}


export interface CmsAuditUserEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAuditUserInfoData;
    rules: object;
}