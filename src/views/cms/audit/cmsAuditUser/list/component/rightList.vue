<template>
  <div class="right-content">
    <div class="audit-cmsAuditUser-operation mb15">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="info"
            @click="refreshList"
          ><el-icon><ele-Refresh /></el-icon>刷新</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            @click="handleAdd"
            v-auth="'api/v1/audit/cmsAuditUser/add'"
          ><el-icon><ele-Plus /></el-icon>新增</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="danger"
            :disabled="multiple"
            @click="handleDelete(null)"
            v-auth="'api/v1/audit/cmsAuditUser/delete'"
          ><el-icon><ele-Delete /></el-icon>删除</el-button>
        </el-col>
      </el-row>
    </div>
    <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" min-width="150px" />
      <el-table-column label="用户" align="center" min-width="150px">
        <template #default="scope">
          <span>{{ getUserName(scope.row.userInfo.userNickname) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核步骤" align="center" min-width="200px">
        <template #default="scope">
          <span>{{ getStepName(scope.row.stepId, scope.row.step) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="栏目" align="center" min-width="200px">
        <template #default="scope">
          <el-tag
            v-for="(category, index) in scope.row.categoryNames ? scope.row.categoryNames.split(',') : []"
            :key="index"
            class="ml-2"
            type="info"
            effect="plain"
          >
            {{ category }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding" min-width="160px" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
            v-auth="'api/v1/audit/cmsAuditUser/edit'"
          ><el-icon><ele-EditPen /></el-icon>修改</el-button>
          <el-button
            type="primary"
            link
            @click="handleDelete(scope.row)"
            v-auth="'api/v1/audit/cmsAuditUser/delete'"
          ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableData.total>0"
      :total="tableData.total"
      v-model:page="tableData.param.pageNum"
      v-model:limit="tableData.param.pageSize"
      @pagination="cmsAuditUserList"
    />

    <!-- 编辑和详情组件 -->
    <ApiV1CmsAuditCmsAuditUserEdit
      ref="editRef"
      :default-user-id="props.selectedUserId"
      @cmsAuditUserList="cmsAuditUserList"
    />
    <ApiV1CmsAuditCmsAuditUserDetail
      ref="detailRef"
      @cmsAuditUserList="cmsAuditUserList"
    />
  </div>
</template>

<script setup lang="ts">
import { toRefs, reactive, onMounted, ref, getCurrentInstance, toRaw, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  listCmsAuditUser,
  getCmsAuditUser,
  delCmsAuditUser,
  addCmsAuditUser,
  updateCmsAuditUser,
} from "/@/api/cms/audit/cmsAuditUser";
import { listCmsAuditStep } from "/@/api/cms/audit/cmsAuditStep";
import { getUserList } from "/@/api/system/user";
import {
  CmsAuditUserTableColumns,
  CmsAuditUserInfoData,
  CmsAuditUserTableDataState,
} from "/@/views/cms/audit/cmsAuditUser/list/component/model";
import ApiV1CmsAuditCmsAuditUserEdit from "/@/views/cms/audit/cmsAuditUser/list/component/edit.vue";
import ApiV1CmsAuditCmsAuditUserDetail from "/@/views/cms/audit/cmsAuditUser/list/component/detail.vue";

defineOptions({ name: "RightList" });

// 定义props和emits
const props = defineProps({
  selectedUserId: {
    type: Number,
    default: undefined
  }
});

const emit = defineEmits([]);

const { proxy } = <any>getCurrentInstance();
const loading = ref(false);

const editRef = ref();
const detailRef = ref();

// 用户选项
const userOptions = ref<any[]>([]);
const userLoading = ref(false);
const userSearchKeyword = ref('');
const userMap = ref<Map<number, string>>(new Map());

// 审核步骤选项
const stepOptions = ref<any[]>([]);
const stepLoading = ref(false);
const stepMap = ref<Map<number, string>>(new Map());

// 非多个禁用
const multiple = ref(true);



const state = reactive<CmsAuditUserTableDataState>({
  ids: [],
  tableData: {
    data: [],
    total: 0,
    loading: false,
    param: {
      pageNum: 1,
      pageSize: 10,
      userId: undefined,
    },
  },
});

const { tableData } = toRefs(state);

// 加载用户列表
const loadUserOptions = (keyword = '') => {
  userLoading.value = true;
  getUserList({
    pageNum: 1,
    pageSize: 1000,
    keyWords: keyword
  }).then((res: any) => {
    if (res.code === 0 && res.data && res.data.userList) {
      // 将用户列表转换为选项格式
      userOptions.value = res.data.userList.map((user: any) => ({
        value: user.id,
        label: `${user.userNickname || user.userName}${user.mobile ? ` (${user.mobile})` : ''}`
      }));

      // 更新用户映射
      res.data.userList.forEach((user: any) => {
        userMap.value.set(user.id, user.userNickname || user.userName);
      });
    }
    userLoading.value = false;
  }).catch(() => {
    userLoading.value = false;
  });
};


// 获取用户名称
const getUserName = (userId: number) => {
  if (!userId) return '';

  // 如果用户映射中有该用户，直接返回
  if (userMap.value.has(userId)) {
    return userMap.value.get(userId);
  }

  // 如果用户选项中有该用户，更新映射并返回
  const userOption = userOptions.value.find(option => option.value === userId);
  if (userOption) {
    userMap.value.set(userId, userOption.label);
    return userOption.label;
  }

  // 如果没有找到用户，返回ID
  return userId;
};

// 监听选中的用户ID变化
watch(() => props.selectedUserId, (newVal) => {
  if (newVal !== state.tableData.param.userId) {
    state.tableData.param.userId = newVal;
    state.tableData.param.pageNum = 1; // 重置到第一页
    cmsAuditUserList();
  }
}, { immediate: true });

// 组件挂载时初始化数据
onMounted(() => {
  initTableData();
});

// 加载审核步骤列表
const loadStepOptions = () => {
  stepLoading.value = true;
  listCmsAuditStep({
    pageNum: 1,
    pageSize: 1000
  }).then((res: any) => {
    if (res.code === 0 && res.data && res.data.list) {
      // 将审核步骤列表转换为选项格式
      stepOptions.value = res.data.list.map((step: any) => ({
        value: step.id,
        label: `${step.title} (第${step.step}审)`,
        step: step.step
      }));

      // 更新步骤映射
      res.data.list.forEach((step: any) => {
        stepMap.value.set(step.id, `${step.title} (第${step.step}审)`);
      });
    }
    stepLoading.value = false;
  }).catch(() => {
    stepLoading.value = false;
  });
};

// 获取步骤名称
const getStepName = (stepId: number, step: number) => {
  if (!stepId) return step ? `第${step}审` : '未知';

  // 如果步骤映射中有该步骤，直接返回
  if (stepMap.value.has(stepId)) {
    return stepMap.value.get(stepId);
  }

  // 如果步骤选项中有该步骤，更新映射并返回
  const stepOption = stepOptions.value.find(option => option.value === stepId);
  if (stepOption) {
    stepMap.value.set(stepId, stepOption.label);
    return stepOption.label;
  }

  // 如果没有找到步骤，返回步骤数字
  return step ? `第${step}审` : '未知';
};

// 初始化表格数据
const initTableData = () => {
  // 加载审核步骤选项
  if (stepOptions.value.length === 0) {
    loadStepOptions();
  }
  cmsAuditUserList();
};



// 获取列表数据
const cmsAuditUserList = () => {
  loading.value = true;
  listCmsAuditUser(state.tableData.param).then((res: any) => {
    let list = res.data.list ?? [];
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false;
  });
};

// 刷新列表
const refreshList = () => {
  // 重新加载列表数据
  cmsAuditUserList();
  // 显示刷新成功提示
  ElMessage.success('刷新成功');
};



// 多选框选中数据
const handleSelectionChange = (selection: Array<CmsAuditUserInfoData>) => {
  state.ids = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

const handleAdd = () => {
  // 如果有选中的用户ID，则传递给编辑组件
  editRef.value.openDialog(undefined, props.selectedUserId);
};

const handleUpdate = (row: CmsAuditUserTableColumns | null) => {
  if (!row) {
    row = state.tableData.data.find((item: CmsAuditUserTableColumns) => {
      return item.id === state.ids[0];
    }) as CmsAuditUserTableColumns;
  }
  console.log('修改操作，传递的行数据:', toRaw(row));
  editRef.value.openDialog(toRaw(row));
};

const handleDelete = (row: CmsAuditUserTableColumns | null) => {
  let msg = '你确定要删除所选数据？';
  let id: number[] = [];
  if (row) {
    msg = `此操作将永久删除数据，是否继续?`;
    id = [row.id];
  } else {
    id = state.ids;
  }
  if (id.length === 0) {
    ElMessage.error('请选择要删除的数据。');
    return;
  }
  ElMessageBox.confirm(msg, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      delCmsAuditUser(id).then(() => {
        ElMessage.success('删除成功');
        cmsAuditUserList();
      });
    })
    .catch(() => {});
};

const handleView = (row: CmsAuditUserTableColumns) => {
  detailRef.value.openDialog(toRaw(row));
};
</script>

<style lang="scss" scoped>
.right-content {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.colBlock {
  display: block;
}

.colNone {
  display: none;
}

.ml-2 {
  margin: 3px;
}
</style>
