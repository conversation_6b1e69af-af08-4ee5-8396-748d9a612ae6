<template>
  <div class="audit-cmsAuditCategory-container">
    <el-card shadow="hover">
      <div class="audit-cmsAuditCategory-content">
        <!-- 左侧栏目树 -->
        <LeftTree @node-click="handleNodeClick" />

        <!-- 右侧内容 -->
        <RightList
          :selected-category-id="selectedCategoryId"
          @update:selected-category-id="handleCategoryIdUpdate"
        />
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import LeftTree from "./component/leftTree.vue";
import RightList from "./component/rightList.vue";

defineOptions({ name: "apiV1CmsAuditCmsAuditCategoryList"});

// 当前选中的栏目 ID
const selectedCategoryId = ref<number | undefined>(undefined);

// 处理节点点击
const handleNodeClick = (node: any) => {
  if (node && node.id) {
    selectedCategoryId.value = node.id;
  } else {
    selectedCategoryId.value = undefined;
  }
};

// 处理栏目 ID 更新
const handleCategoryIdUpdate = (newCategoryId: number | undefined) => {
  selectedCategoryId.value = newCategoryId;
};
</script>
<style lang="scss" scoped>
.audit-cmsAuditCategory-container {
  height: 100%;

  .audit-cmsAuditCategory-content {
    display: flex;
    height: calc(100vh - 200px);
  }
}
</style>