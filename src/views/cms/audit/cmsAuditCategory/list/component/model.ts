export interface CmsAuditCategoryTableColumns {
    id:number;  // ID
    userIds:string;  // 审核人员ID（逗号分隔的字符串）
    categoryId:number;  // 栏目ID
}


export interface CmsAuditCategoryInfoData {
    id:number|undefined;        // ID
    userIds:string|undefined; // 审核人员ID（逗号分隔的字符串）
    stepId:number|undefined; // 审核步骤ID
    step:number|undefined; // 审核步骤数字
    categoryId:number|undefined; // 栏目ID
}


export interface CmsAuditCategoryTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAuditCategoryTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            id: number|undefined;
            userIds: string|undefined;
            categoryId: number|undefined;
            dateRange: string[];
        };
    };
}


export interface CmsAuditCategoryEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAuditCategoryInfoData;
    rules: object;
}