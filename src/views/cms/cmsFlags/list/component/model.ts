export interface CmsFlagsTableColumns {    
    id:number;  // ID    
    name:string;  // 属性名称    
    sort:number;  // 排序    
    createdAt:string;  // 创建时间    
    categoryIds:string;  // 栏目ids    
}


export interface CmsFlagsInfoData {    
    id:number|undefined;        // ID    
    name:string|undefined; // 属性名称    
    sort:number|undefined; // 排序    
    createdAt:string|undefined; // 创建时间    
    categoryIds:string|undefined; // 栏目ids    
    updatedAt:string|undefined; // 更新时间    
    deletedAt:string|undefined; // 删除时间    
}


export interface CmsFlagsTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsFlagsTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            name: string|undefined;            
            sort: number|undefined;            
            createdAt: string|undefined;            
            categoryIds: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsFlagsEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsFlagsInfoData;
    rules: object;
}