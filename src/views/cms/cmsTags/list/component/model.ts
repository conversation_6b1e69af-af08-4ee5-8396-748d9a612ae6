export interface CmsTagsTableColumns {    
    id:number;  // ID    
    name:string;  // 标签名称    
    createdAt:string;  // 创建日期    
    sort:number;  // 排序    
}


export interface CmsTagsInfoData {    
    id:number|undefined;        // ID    
    name:string|undefined; // 标签名称    
    createdAt:string|undefined; // 创建日期    
    updatedAt:string|undefined; // 更新日期    
    sort:number|undefined; // 排序    
}


export interface CmsTagsTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsTagsTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            name: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsTagsEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsTagsInfoData;
    rules: object;
}