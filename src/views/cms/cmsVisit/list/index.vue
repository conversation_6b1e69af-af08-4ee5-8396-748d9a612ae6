<template>
  <div class="cms-visit-container">
    <el-card shadow="hover" class="mb20">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全站" name="all"></el-tab-pane>
        <el-tab-pane label="分类" name="category"></el-tab-pane>
        <el-tab-pane label="文章" name="article"></el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 全站统计组件 -->
    <VisitStats v-show="activeTab === 'all'" ref="statsRef" :type="currentType" v-model:loading="loading" />

    <!-- 分类统计组件 -->
    <CategoryStats v-show="activeTab === 'category'" ref="categoryRef" v-model:loading="loading" />

    <!-- 文章统计组件 -->
    <ArticleStats v-show="activeTab === 'article'" ref="articleRef" v-model:loading="loading" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import VisitStats from './component/index.vue';
import CategoryStats from './component/category.vue';
import ArticleStats from './component/article.vue';

defineOptions({ name: "cmsVisitList" });

const loading = ref(false);
const activeTab = ref('all');
const currentType = ref('');
const statsRef = ref();
const categoryRef = ref();
const articleRef = ref();

// 初始化所有组件
onMounted(() => {
  // 默认选中全站标签
  activeTab.value = 'all';
  currentType.value = '';

  // 初始加载全站数据
  if (statsRef.value) {
    statsRef.value.getList();
  }
});

// 监听标签变化
watch(() => activeTab.value, (newTab, oldTab) => {
  if (newTab === oldTab) return;

  // 根据标签加载相应数据
  switch(newTab) {
    case 'all':
      currentType.value = '';
      if (statsRef.value) {
        statsRef.value.getList();
      }
      break;
    case 'category':
      if (categoryRef.value) {
        categoryRef.value.getCategoryVisitStats();
      }
      break;
    case 'article':
      if (articleRef.value) {
        articleRef.value.getArticleVisitStats();
      }
      break;
  }
});

// 处理标签点击
const handleTabClick = (tab: any) => {
  activeTab.value = tab.props.name;
};
</script>

<style scoped lang="scss">
.cms-visit-container {
  padding: 10px;
}
</style>