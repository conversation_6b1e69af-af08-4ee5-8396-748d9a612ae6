<template>
  <div class="cms-visit-article-container">
    <el-card shadow="hover">
      <el-table v-loading="loading" :data="articleData" border>
        <el-table-column label="ID" prop="id" width="80" align="center" />
        <el-table-column label="文章标题" prop="title" min-width="250">
          <template #default="{row}">
            <el-link type="primary" :href="row.url" target="_blank" :underline="false">{{ row.article.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="分类" prop="category.name" width="120" align="center" />
        <el-table-column label="访问量" prop="count" width="100" align="center" />
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getArticleVisitStats"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ArticleCmsVist } from "/@/api/cms/cmsVisit";

defineOptions({ name: "cmsVisitArticle" });

const { proxy } = <any>getCurrentInstance();
const loading = ref(false);
const articleData = ref([]);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  categoryId: undefined,
  startTime: undefined,
  endTime: undefined
});

// 获取文章访问统计数据
const getArticleVisitStats = async () => {
  loading.value = true;
  try {
    // 使用 ArticleCmsVist 接口获取文章访问统计数据
    const res = await ArticleCmsVist(queryParams);

    if (res.data) {
      // 获取文章列表和总数
      articleData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      articleData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取文章访问统计失败:', error);
    articleData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};



// 不在组件挂载时自动调用获取数据的方法，而是由父组件控制调用时机
onMounted(() => {
  // 不自动调用
});

// 暴露方法给父组件
defineExpose({
  getArticleVisitStats
});
</script>

<style scoped lang="scss">
.cms-visit-article-container {
  margin-top: 20px;
}
</style>
