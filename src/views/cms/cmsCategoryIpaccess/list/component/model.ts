export interface CmsCategoryIpaccessTableColumns {    
    id:number;  //    
    name:string;  // 名称    
    categoryIds:string;  // 栏目IDS    
    iptable:string;  // IP表    
    createdAt:string;  // 创建时间    
}


export interface CmsCategoryIpaccessInfoData {    
    id:number|undefined;        //    
    name:string|undefined; // 名称    
    categoryIds:string|undefined; // 栏目IDS    
    iptable:string|undefined; // IP表    
    createdAt:string|undefined; // 创建时间    
    updatedAt:string|undefined; // 更新时间    
}


export interface CmsCategoryIpaccessTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsCategoryIpaccessTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            name: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsCategoryIpaccessEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsCategoryIpaccessInfoData;
    rules: object;
}