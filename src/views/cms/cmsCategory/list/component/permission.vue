<template>
  <div>
    <el-dialog
      title="分类权限"
      v-model="dialogVisible"
      width="1069px"
    >
      <el-form :model="formData" label-width="80px" class="mb-15">
        <el-form-item label="选择角色">
          <el-tree-select
            v-model="formData.roleId"
            :data="roleOptions"
            node-key="id"
            check-strictly
            :props="{
              label: 'name',
              value: 'id',
              children: 'children'
            }"
            :render-after-expand="false"
            placeholder="请选择角色"
            clearable
            @change="handleRoleChange"
          >
            <template #default="{ data }">
              <span>{{ data.name }}</span>
              <span v-if="data.remark" class="text-gray-400 ml-2">({{ data.remark }})</span>
            </template>
          </el-tree-select>
        </el-form-item>
      </el-form>
      <el-table
        :data="categoryPermissionsList"
        row-key="id"
        border
        default-expand-all
        :tree-props="{
          children: 'children',
          hasChildren: 'hasChildren'
        }"
      >
        <el-table-column
          prop="name"
          label="栏目名称"
          min-width="180"
        />
        <el-table-column
          label="权限设置"
          min-width="400"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <span>权限设置</span>
              <el-button
                link
                type="primary"
                @click="handleToggleAllCategories"
                style="margin-right: 10px;"
              >
                {{ isAllCategoriesSelected ? '反选' : '全选' }}
              </el-button>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex items-center">
              <el-button
                v-if="row.type === 'list' "
                link
                type="primary"
                @click="handleToggleAll(row)"
                style="margin-right: 10px;"
              >
                {{ isAllSelected(row) ? '反选' : '全选' }}
              </el-button>
              <el-checkbox-group v-model="row.permissions">
                <template v-if="row.type === 'channel' || row.type === 'page' || row.type === 'jump' ">
                  <el-checkbox
                    value="list"
                  >
                    查看
                  </el-checkbox>
                </template>
                <template v-else-if="row.type === 'list'">
                  <!-- list类型显示所有权限 -->
                  <el-checkbox
                    v-for="(item, index) in permissionOptions"
                    :key="index"
                    :value="item.value"
                    style="margin-right: 20px;"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </template>
              </el-checkbox-group>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, getCurrentInstance, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { listCmsCategoryPermissions, saveCmsCategoryPermissions, listCmsCategory } from '/@/api/cms/cmsCategory';
import { getRoleList } from '/@/api/system/role';

interface CategoryPermission {
  id: number;
  name: string;
  type: string;
  parentId: number;
  permissions: string[];
  children?: CategoryPermission[];
}

interface PermissionOption {
  value: string;
  label: string;
}

export default defineComponent({
  name: 'CategoryPermission',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const { proxy } = getCurrentInstance() as any;
    const dialogVisible = ref(props.modelValue);
    const categoryPermissionsList = ref<CategoryPermission[]>([]);
    const categoryLoading = ref(false);
    const roleOptions = ref([]);
    const formData = ref({
      roleId: undefined
    });

    // 打开对话框
    const openDialog = () => {
      dialogVisible.value = true;
      // 加载角色列表
      getRoles();
    };

    // 获取角色列表
    const getRoles = async () => {
      try {
        const res = await getRoleList({});
        const list = res.data.list || [];
        // 将扁平数据转换为树形结构
        roleOptions.value = proxy.handleTree(list.map((item: any) => ({
          id: item.id,
          name: item.name,
          parentId: item.pid,
          status: item.status,
          remark: item.remark,
          userCnt: item.userCnt,
          dataScope: item.dataScope
        })), 'id', 'parentId');
      } catch (error) {
        ElMessage.error("获取角色列表失败");
      }
    };

    // 获取栏目列表
    const getCategoryList = async () => {
      try {
        const res = await listCmsCategory({});
        const list = res.data.list || [];
        return proxy.handleTree(list.map((item: any) => ({
          id: item.id,
          name: item.name,
          type: item.type,
          parentId: item.parentId,
          permissions: []
        })), 'id', 'parentId');
      } catch (error) {
        ElMessage.error("获取栏目列表失败");
        return [];
      }
    };

    // 获取权限数据
    const getPermissionsData = async (roleId: number) => {
      try {
        const res = await listCmsCategoryPermissions({
          roleId: roleId
        });
        return res.data.list || []; // 返回权限列表数组
      } catch (error) {
        ElMessage.error("获取权限数据失败");
        return [];
      }
    };

    // 合并权限数据到栏目树
    const mergePermissionsToCategory = (categories: any[], permissions: any[]) => {
      // 创建权限映射，key为categoryId，value为权限数组
      const permissionMap = new Map();
      permissions.forEach(item => {
        permissionMap.set(item.categoryId, item.permissions ? item.permissions.split(',').filter(Boolean) : []);
      });

      const mergePerm = (list: any[]) => {
        list.forEach(item => {
          // 从权限映射中获取对应栏目的权限
          item.permissions = permissionMap.get(item.id) || [];

          if (item.children && item.children.length > 0) {
            mergePerm(item.children);
          }
        });
      };

      mergePerm(categories);
      return categories;
    };

    // 获取栏目权限列表
    const getCategoryPermissions = async (roleId?: number) => {
      if (!roleId) {
        categoryPermissionsList.value = [];
        return;
      }

      try {
        categoryLoading.value = true;
        const [categories, permissions] = await Promise.all([
          getCategoryList(),
          getPermissionsData(roleId)
        ]);

        categoryPermissionsList.value = mergePermissionsToCategory(categories, permissions);
      } catch (error) {
        ElMessage.error("获取数据失败");
      } finally {
        categoryLoading.value = false;
      }
    };

    // 角色变更处理
    const handleRoleChange = (value: number) => {
      getCategoryPermissions(value);
    };

    // 定义权限选项
    const permissionOptions = ref<PermissionOption[]>([
      { value: 'list', label: '查看' },
      { value: 'add', label: '新增' },
      { value: 'edit', label: '修改' },
      { value: 'delete', label: '删除' },
      { value: 'push', label: '推送' },
      { value: 'move', label: '移动' },
      { value: 'publish', label: '发布' },
      { value: 'recycle', label: '回收站' },
    ]);

    // 监听 modelValue 变化
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal;
      if (newVal) {
        getRoles();
      } else {
        formData.value.roleId = undefined;
        categoryPermissionsList.value = [];
      }
    });

    // 保存栏目权限
    const handleSave = async () => {
      if (!formData.value.roleId) {
        ElMessage.warning("请先选择角色");
        return;
      }

      try {
        // 收集所有栏目的权限数据（包括子栏目）
        const getAllPermissions = (categories: any[]): any[] => {
          let permissions: any[] = [];
          categories.forEach(item => {
            permissions.push({
              categoryId: item.id,
              permissions: item.permissions.join(',')
            });
            if (item.children && item.children.length > 0) {
              permissions = permissions.concat(getAllPermissions(item.children));
            }
          });
          return permissions;
        };

        const updatedPermissions = getAllPermissions(categoryPermissionsList.value);

        await saveCmsCategoryPermissions({
          roleId: formData.value.roleId,
          permissions: updatedPermissions
        });
        ElMessage.success("权限保存成功");
        handleCancel();
      } catch (error) {
        ElMessage.error("保存权限失败，请重试");
      }
    };

    // 取消操作
    const handleCancel = () => {
      dialogVisible.value = false;
      emit('update:modelValue', false);
      // 重置表单数据
      formData.value.roleId = undefined;
      categoryPermissionsList.value = [];
    };

    // 检查是否全选
    const isAllSelected = (row: any) => {
     if (row.type === 'list') {
        return permissionOptions.value.every(option => row.permissions.includes(option.value));
      } else {
        return row.permissions.includes('list');
      }
    };

    // 处理全选/反选
    const handleToggleAll = (row: any) => {
      if (row.type === 'list') {
        if (isAllSelected(row)) {
          row.permissions = [];
        } else {
          row.permissions = permissionOptions.value.map(option => option.value);
        }
      } else {
        row.permissions = isAllSelected(row) ? [] : ['list'];
      }
    };

    const isAllCategoriesSelected = computed(() => {
      if (!categoryPermissionsList.value.length) return false;
      return categoryPermissionsList.value.every(category => {
        if (category.type === 'list') {
          return permissionOptions.value.every(option => category.permissions.includes(option.value));
        } else {
          return category.permissions.includes('list');
        }
      });
    });

    const handleToggleAllCategories = () => {
      const setPermissions = (categories: any[], isSelect: boolean) => {
        categories.forEach(category => {
          if (category.type === 'list') {
            category.permissions = isSelect ? permissionOptions.value.map(option => option.value) : [];
          } else {
            category.permissions = isSelect ? ['list'] : [];
          }
          if (category.children && category.children.length > 0) {
            setPermissions(category.children, isSelect);
          }
        });
      };

      setPermissions(categoryPermissionsList.value, !isAllCategoriesSelected.value);
    };

    return {
      dialogVisible,
      categoryPermissionsList,
      permissionOptions,
      handleSave,
      handleCancel,
      roleOptions,
      formData,
      handleRoleChange,
      isAllSelected,
      openDialog,
      handleToggleAll,
      isAllCategoriesSelected,
      handleToggleAllCategories
    };
  }
});
</script>

<style lang="scss" scoped>
.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}
</style>