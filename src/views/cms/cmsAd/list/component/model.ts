export interface CmsAdTableColumns {
    id:number;  // ID
    name:string;  // 广告名称
    type:string;  // 类型
    url:string;  // 跳转链接
    createdAt:string;  // 创建日期
    sort:number;  // 排序
    status:number;  // 状态
    position:number;  // 广告位置
    linkedCmsAdCmsAdPosition:LinkedCmsAdCmsAdPosition;
}


export interface CmsAdInfoData {
    id:number|undefined;        // ID
    name:string|undefined; // 广告名称
    type:string; // 类型
    content:string|undefined; // 广告内容
    url:string|undefined; // 跳转链接
    createdAt:string|undefined; // 创建日期
    updatedAt:string|undefined; // 更新日期
    sort:number|undefined; // 排序
    status:string; // 状态
    position:number|undefined; // 广告位置
    linkedCmsAdCmsAdPosition:LinkedCmsAdCmsAdPosition;
}


export interface LinkedCmsAdCmsAdPosition {
    id:number|undefined;    // ID
    name:string|undefined;    // 位置名称
}


export interface CmsAdTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAdTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            name: string|undefined;
            position: number|undefined;
            dateRange: string[];
        };
    };
}


export interface CmsAdEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAdInfoData;
    rules: object;
}
