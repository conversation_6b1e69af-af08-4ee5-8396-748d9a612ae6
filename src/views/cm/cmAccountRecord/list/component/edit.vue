<template>  
  <div class="cm-cmAccountRecord-edit">
    <!-- 添加或修改账号备案对话框 -->
    <el-dialog v-model="isShowDialog" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.cm-cmAccountRecord-edit .el-dialog', '.cm-cmAccountRecord-edit .el-dialog__header']">{{(!formData.id || formData.id==0?'添加':'修改')+'账号备案'}}</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">        
        <el-form-item label="网站/应用名称" prop="platformName">
          <el-input v-model="formData.platformName" placeholder="请输入网站/应用名称" />
        </el-form-item>          
        <el-form-item label="平台类型" prop="platformType">
          <el-select filterable clearable v-model="formData.platformType" placeholder="请选择平台类型" >
            <el-option
              v-for="dict in platformTypeOptions"
              :key="dict.value"
              :label="dict.label"              
                  :value="dict.value"              
            ></el-option>
          </el-select>
        </el-form-item>        
        <el-form-item label="官网网址" prop="officialUrl">
          <el-input v-model="formData.officialUrl" placeholder="请输入官网网址" />
        </el-form-item>          
        <el-form-item label="账号用途" prop="accountPurpose">
          <el-select filterable clearable v-model="formData.accountPurpose" placeholder="请选择账号用途" >
            <el-option
              v-for="dict in accountPurposeOptions"
              :key="dict.value"
              :label="dict.label"              
                  :value="dict.value"              
            ></el-option>
          </el-select>
        </el-form-item>        
        <el-form-item label="注册国家/地区" prop="registeredRegion">
          <el-input v-model="formData.registeredRegion" placeholder="请输入注册国家/地区" />
        </el-form-item>        
        <el-form-item label="账号" prop="username">
          <el-input v-model="formData.username" placeholder="请输入账号" />
        </el-form-item>        
        <el-form-item label="登录邮箱" prop="loginEmail">
          <el-input v-model="formData.loginEmail" placeholder="请输入登录邮箱" />
        </el-form-item>        
        <el-form-item label="绑定手机号" prop="boundPhoneNumber">
          <el-input v-model="formData.boundPhoneNumber" placeholder="请输入绑定手机号" />
        </el-form-item>        
        <el-form-item label="登录密码" prop="loginPassword">
          <el-input v-model="formData.loginPassword" placeholder="请输入登录密码" />
        </el-form-item>        
        <el-form-item label="密码更新时间" prop="passwordUpdateDate">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.passwordUpdateDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择密码更新时间">
          </el-date-picker>
        </el-form-item>        
        <el-form-item label="密码提示问题" prop="securityQuestion">
          <el-input v-model="formData.securityQuestion" placeholder="请输入密码提示问题" />
        </el-form-item>        
        <el-form-item label="密码提示答案" prop="securityAnswer">
          <el-input v-model="formData.securityAnswer" placeholder="请输入密码提示答案" />
        </el-form-item>        
        <el-form-item label="两步验证状态" prop="twoFaStatus">
          <el-radio-group v-model="formData.twoFaStatus">
            <el-radio
              v-for="dict in twoFaStatusOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>        
        <el-form-item label="备用验证邮箱" prop="recoveryEmail">
          <el-input v-model="formData.recoveryEmail" placeholder="请输入备用验证邮箱" />
        </el-form-item>        
        <el-form-item label="备用验证手机号" prop="recoveryPhone">
          <el-input v-model="formData.recoveryPhone" placeholder="请输入备用验证手机号" />
        </el-form-item>        
        <el-form-item label="安全密钥标识" prop="securityKey">
          <el-input v-model="formData.securityKey" placeholder="请输入安全密钥标识" />
        </el-form-item>        
        <el-form-item label="账号恢复码" prop="recoveryCodes">
          <el-input v-model="formData.recoveryCodes" placeholder="请输入账号恢复码" />
        </el-form-item>        
        <el-form-item label="显示名称" prop="displayName">
          <el-input v-model="formData.displayName" placeholder="请输入显示名称" />
        </el-form-item>          
        <el-form-item label="用户角色" prop="userRole">
          <el-select filterable clearable v-model="formData.userRole" placeholder="请选择用户角色" >
            <el-option
              v-for="dict in userRoleOptions"
              :key="dict.value"
              :label="dict.label"              
                  :value="dict.value"              
            ></el-option>
          </el-select>
        </el-form-item>        
        <el-form-item label="绑定社交账号" prop="linkedSocialAccounts">
          <el-input v-model="formData.linkedSocialAccounts" placeholder="请输入绑定社交账号" />
        </el-form-item>        
        <el-form-item label="注册日期" prop="registrationDate">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.registrationDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择注册日期">
          </el-date-picker>
        </el-form-item>        
        <el-form-item label="最后登录时间" prop="lastLoginTime">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.lastLoginTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择最后登录时间">
          </el-date-picker>
        </el-form-item>        
        <el-form-item label="账号有效期" prop="accountExpiryDate">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.accountExpiryDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择账号有效期">
          </el-date-picker>
        </el-form-item>        
        <el-form-item label="账号状态" prop="accountStatus">
          <el-radio-group v-model="formData.accountStatus">
            <el-radio
              v-for="dict in accountStatusOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>        
        <el-form-item label="风险提示" prop="riskAlert">
          <el-input v-model="formData.riskAlert" placeholder="请输入风险提示" />
        </el-form-item>        
        <el-form-item label="绑定支付方式" prop="paymentMethod">
          <el-input v-model="formData.paymentMethod" placeholder="请输入绑定支付方式" />
        </el-form-item>        
        <el-form-item label="订阅计划" prop="subscriptionPlan">
          <el-input v-model="formData.subscriptionPlan" placeholder="请输入订阅计划" />
        </el-form-item>        
        <el-form-item label="自动续费状态" prop="autoRenewalStatus">
          <el-radio-group v-model="formData.autoRenewalStatus">
            <el-radio
              v-for="dict in autoRenewalStatusOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>        
        <el-form-item label="API密钥" prop="apiKey">
          <el-input v-model="formData.apiKey" placeholder="请输入API密钥" />
        </el-form-item>        
        <el-form-item label="API权限范围" prop="apiPermissions">
          <el-input v-model="formData.apiPermissions" placeholder="请输入API权限范围" />
        </el-form-item>        
        <el-form-item label="API密钥有效期" prop="apiExpiryDate">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.apiExpiryDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择API密钥有效期">
          </el-date-picker>
        </el-form-item>        
        <el-form-item label="特殊要求" prop="specialRequirements">
          <el-input v-model="formData.specialRequirements" placeholder="请输入特殊要求" />
        </el-form-item>        
        <el-form-item label="历史记录" prop="historyLog">
          <el-input v-model="formData.historyLog" placeholder="请输入历史记录" />
        </el-form-item>        
        <el-form-item label="文件附件路径" prop="attachments">
          <el-input v-model="formData.attachments" placeholder="请输入文件附件路径" />
        </el-form-item>        
        <el-form-item label="管理人" prop="manageUser">
          <el-input v-model="formData.manageUser" placeholder="请输入管理人" />
        </el-form-item>        
        <el-form-item label="管理人手机号" prop="manageMobile">
          <el-input v-model="formData.manageMobile" placeholder="请输入管理人手机号" />
        </el-form-item>        
        <el-form-item label="使用人" prop="useUser">
          <el-input v-model="formData.useUser" placeholder="请输入使用人" />
        </el-form-item>        
        <el-form-item label="使用人手机号" prop="useMobile">
          <el-input v-model="formData.useMobile" placeholder="请输入使用人手机号" />
        </el-form-item>       
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit" :disabled="loading">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, toRefs, ref,unref,getCurrentInstance,computed } from 'vue';
import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
import {
  listCmAccountRecord,
  getCmAccountRecord,
  delCmAccountRecord,
  addCmAccountRecord,
  updateCmAccountRecord,  
} from "/@/api/cm/cmAccountRecord";
import {
  CmAccountRecordTableColumns,
  CmAccountRecordInfoData,
  CmAccountRecordTableDataState,
  CmAccountRecordEditState
} from "/@/views/cm/cmAccountRecord/list/component/model"
defineOptions({ name: "ApiV1CmCmAccountRecordEdit"})
const emit = defineEmits(['cmAccountRecordList'])
  const props = defineProps({    
    platformTypeOptions:{
      type:Array,
      default:()=>[]
    },    
    accountPurposeOptions:{
      type:Array,
      default:()=>[]
    },    
    twoFaStatusOptions:{
      type:Array,
      default:()=>[]
    },    
    userRoleOptions:{
      type:Array,
      default:()=>[]
    },    
    accountStatusOptions:{
      type:Array,
      default:()=>[]
    },    
    autoRenewalStatusOptions:{
      type:Array,
      default:()=>[]
    },    
  })
const {proxy} = <any>getCurrentInstance()
const formRef = ref<HTMLElement | null>(null);
const menuRef = ref();
const state = reactive<CmAccountRecordEditState>({
  loading:false,
  isShowDialog: false,
  formData: {    
    id: undefined,    
    platformName: undefined,    
    platformType: undefined,    
    officialUrl: undefined,    
    accountPurpose: undefined,    
    registeredRegion: undefined,    
    username: undefined,    
    loginEmail: undefined,    
    boundPhoneNumber: undefined,    
    loginPassword: undefined,    
    passwordUpdateDate: undefined,    
    securityQuestion: undefined,    
    securityAnswer: undefined,    
    twoFaStatus: undefined,    
    recoveryEmail: undefined,    
    recoveryPhone: undefined,    
    securityKey: undefined,    
    recoveryCodes: undefined,    
    displayName: undefined,    
    userRole: undefined,    
    linkedSocialAccounts: undefined,    
    registrationDate: undefined,    
    lastLoginTime: undefined,    
    accountExpiryDate: undefined,    
    accountStatus: undefined,    
    riskAlert: undefined,    
    paymentMethod: undefined,    
    subscriptionPlan: undefined,    
    autoRenewalStatus: undefined,    
    apiKey: undefined,    
    apiPermissions: undefined,    
    apiExpiryDate: undefined,    
    specialRequirements: undefined,    
    historyLog: undefined,    
    attachments: undefined,    
    manageUser: undefined,    
    manageMobile: undefined,    
    useUser: undefined,    
    useMobile: undefined,    
    deptId: undefined,    
    createdBy: undefined,    
    updatedBy: undefined,    
    createdAt: undefined,    
    updatedAt: undefined,    
    deletedAt: undefined,    
  },
  // 表单校验
  rules: {    
    id : [
        { required: true, message: "ID不能为空", trigger: "blur" }
    ],    
    platformName : [
        { required: true, message: "网站/应用名称不能为空", trigger: "blur" }
    ],    
    platformType : [
        { required: true, message: "平台类型不能为空", trigger: "blur" }
    ],    
    officialUrl : [
        { required: true, message: "官网网址不能为空", trigger: "blur" }
    ],    
    accountPurpose : [
        { required: true, message: "账号用途不能为空", trigger: "blur" }
    ],    
    username : [
        { required: true, message: "账号不能为空", trigger: "blur" }
    ],    
    loginEmail : [
        { required: true, message: "登录邮箱不能为空", trigger: "blur" }
    ],    
    loginPassword : [
        { required: true, message: "登录密码不能为空", trigger: "blur" }
    ],    
    twoFaStatus : [
        { required: true, message: "两步验证状态不能为空", trigger: "blur" }
    ],    
    displayName : [
        { required: true, message: "显示名称不能为空", trigger: "blur" }
    ],    
    registrationDate : [
        { required: true, message: "注册日期不能为空", trigger: "blur" }
    ],    
    accountStatus : [
        { required: true, message: "账号状态不能为空", trigger: "blur" }
    ],    
    autoRenewalStatus : [
        { required: true, message: "自动续费状态不能为空", trigger: "blur" }
    ],    
  }
});
const { loading,isShowDialog,formData,rules } = toRefs(state);
// 打开弹窗
const openDialog = (row?: CmAccountRecordInfoData) => {
  resetForm();
  if(row) {
    getCmAccountRecord(row.id!).then((res:any)=>{
      const data = res.data;      
      data.platformType = ''+data.platformType      
      data.accountPurpose = ''+data.accountPurpose      
      data.twoFaStatus = ''+data.twoFaStatus      
      data.userRole = ''+data.userRole      
      data.accountStatus = ''+data.accountStatus      
      data.autoRenewalStatus = ''+data.autoRenewalStatus      
      state.formData = data;
  })
}
  state.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.isShowDialog = false;
};
defineExpose({
  openDialog,
});
// 取消
const onCancel = () => {
  closeDialog();
};
// 提交
const onSubmit = () => {
  const formWrap = unref(formRef) as any;
  if (!formWrap) return;
  formWrap.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      if(!state.formData.id || state.formData.id===0){
        //添加
      addCmAccountRecord(state.formData).then(()=>{
          ElMessage.success('添加成功');
          closeDialog(); // 关闭弹窗
          emit('cmAccountRecordList')
        }).finally(()=>{
          state.loading = false;
        })
      }else{
        //修改
      updateCmAccountRecord(state.formData).then(()=>{
          ElMessage.success('修改成功');
          closeDialog(); // 关闭弹窗
          emit('cmAccountRecordList')
        }).finally(()=>{
          state.loading = false;
        })
      }
    }
  });
};
const resetForm = ()=>{
  state.formData = {    
    id: undefined,    
    platformName: undefined,    
    platformType: undefined,    
    officialUrl: undefined,    
    accountPurpose: undefined,    
    registeredRegion: undefined,    
    username: undefined,    
    loginEmail: undefined,    
    boundPhoneNumber: undefined,    
    loginPassword: undefined,    
    passwordUpdateDate: undefined,    
    securityQuestion: undefined,    
    securityAnswer: undefined,    
    twoFaStatus: '' ,    
    recoveryEmail: undefined,    
    recoveryPhone: undefined,    
    securityKey: undefined,    
    recoveryCodes: undefined,    
    displayName: undefined,    
    userRole: undefined,    
    linkedSocialAccounts: undefined,    
    registrationDate: undefined,    
    lastLoginTime: undefined,    
    accountExpiryDate: undefined,    
    accountStatus: '' ,    
    riskAlert: undefined,    
    paymentMethod: undefined,    
    subscriptionPlan: undefined,    
    autoRenewalStatus: '' ,    
    apiKey: undefined,    
    apiPermissions: undefined,    
    apiExpiryDate: undefined,    
    specialRequirements: undefined,    
    historyLog: undefined,    
    attachments: undefined,    
    manageUser: undefined,    
    manageMobile: undefined,    
    useUser: undefined,    
    useMobile: undefined,    
    deptId: undefined,    
    createdBy: undefined,    
    updatedBy: undefined,    
    createdAt: undefined,    
    updatedAt: undefined,    
    deletedAt: undefined,    
  }  
};
</script>
<style scoped>  
  .kv-label{margin-bottom: 15px;font-size: 14px;}
  .mini-btn i.el-icon{margin: unset;}
  .kv-row{margin-bottom: 12px;}
</style>