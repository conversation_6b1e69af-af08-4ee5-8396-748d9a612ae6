<template>
  <!-- 账号备案详情抽屉 -->  
  <div class="cm-cmAccountRecord-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>账号备案详情</h4>
      </template>
      <el-descriptions
              class="margin-top"
              :column="3"
              border
              style="margin: 8px;"
      >        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  ID
                </div>
              </template>
              {{ formData.id }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  网站/应用名称
                </div>
              </template>
              {{ formData.platformName }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    平台类型
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.platformType, platformTypeOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  官网网址
                </div>
              </template>
              {{ formData.officialUrl }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    账号用途
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.accountPurpose, accountPurposeOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  注册国家/地区
                </div>
              </template>
              {{ formData.registeredRegion }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  账号
                </div>
              </template>
              {{ formData.username }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  登录邮箱
                </div>
              </template>
              {{ formData.loginEmail }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  绑定手机号
                </div>
              </template>
              {{ formData.boundPhoneNumber }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  登录密码
                </div>
              </template>
              {{ formData.loginPassword }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                密码更新时间
              </div>
            </template>
            {{ proxy.parseTime(formData.passwordUpdateDate, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  密码提示问题
                </div>
              </template>
              {{ formData.securityQuestion }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  密码提示答案
                </div>
              </template>
              {{ formData.securityAnswer }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    两步验证状态
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.twoFaStatus, twoFaStatusOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  备用验证邮箱
                </div>
              </template>
              {{ formData.recoveryEmail }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  备用验证手机号
                </div>
              </template>
              {{ formData.recoveryPhone }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  安全密钥标识
                </div>
              </template>
              {{ formData.securityKey }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  账号恢复码
                </div>
              </template>
              {{ formData.recoveryCodes }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  显示名称
                </div>
              </template>
              {{ formData.displayName }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    用户角色
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.userRole, userRoleOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  绑定社交账号
                </div>
              </template>
              {{ formData.linkedSocialAccounts }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                注册日期
              </div>
            </template>
            {{ proxy.parseTime(formData.registrationDate, '{y}-{m}-{d}') }}
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                最后登录时间
              </div>
            </template>
            {{ proxy.parseTime(formData.lastLoginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                账号有效期
              </div>
            </template>
            {{ proxy.parseTime(formData.accountExpiryDate, '{y}-{m}-{d}') }}
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    账号状态
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.accountStatus, accountStatusOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  风险提示
                </div>
              </template>
              {{ formData.riskAlert }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  绑定支付方式
                </div>
              </template>
              {{ formData.paymentMethod }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  订阅计划
                </div>
              </template>
              {{ formData.subscriptionPlan }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    自动续费状态
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.autoRenewalStatus, autoRenewalStatusOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  API密钥
                </div>
              </template>
              {{ formData.apiKey }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  API权限范围
                </div>
              </template>
              {{ formData.apiPermissions }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                API密钥有效期
              </div>
            </template>
            {{ proxy.parseTime(formData.apiExpiryDate, '{y}-{m}-{d}') }}
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  特殊要求
                </div>
              </template>
              {{ formData.specialRequirements }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  历史记录
                </div>
              </template>
              {{ formData.historyLog }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  文件附件路径
                </div>
              </template>
              {{ formData.attachments }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  管理人
                </div>
              </template>
              {{ formData.manageUser }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  管理人手机号
                </div>
              </template>
              {{ formData.manageMobile }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  使用人
                </div>
              </template>
              {{ formData.useUser }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  使用人手机号
                </div>
              </template>
              {{ formData.useMobile }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  创建部门
                </div>
              </template>
              {{ formData.deptId }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  创建人
                </div>
              </template>
              {{ formData.createdBy }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  修改人
                </div>
              </template>
              {{ formData.updatedBy }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                创建时间
              </div>
            </template>
            {{ proxy.parseTime(formData.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>        
      </el-descriptions>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listCmAccountRecord,
    getCmAccountRecord,
    delCmAccountRecord,
    addCmAccountRecord,
    updateCmAccountRecord,    
  } from "/@/api/cm/cmAccountRecord";  
  import {
    CmAccountRecordTableColumns,
    CmAccountRecordInfoData,
    CmAccountRecordTableDataState,
    CmAccountRecordEditState
  } from "/@/views/cm/cmAccountRecord/list/component/model"
  defineOptions({ name: "ApiV1CmCmAccountRecordDetail"})  
  const props = defineProps({    
    platformTypeOptions:{
      type:Array,
      default:()=>[]
    },    
    accountPurposeOptions:{
      type:Array,
      default:()=>[]
    },    
    twoFaStatusOptions:{
      type:Array,
      default:()=>[]
    },    
    userRoleOptions:{
      type:Array,
      default:()=>[]
    },    
    accountStatusOptions:{
      type:Array,
      default:()=>[]
    },    
    autoRenewalStatusOptions:{
      type:Array,
      default:()=>[]
    },    
  })  
  const {proxy} = <any>getCurrentInstance()
  const formRef = ref<HTMLElement | null>(null);
  const menuRef = ref();  
  const state = reactive<CmAccountRecordEditState>({
    loading:false,
    isShowDialog: false,
    formData: {      
      id: undefined,      
      platformName: undefined,      
      platformType: undefined,      
      officialUrl: undefined,      
      accountPurpose: undefined,      
      registeredRegion: undefined,      
      username: undefined,      
      loginEmail: undefined,      
      boundPhoneNumber: undefined,      
      loginPassword: undefined,      
      passwordUpdateDate: undefined,      
      securityQuestion: undefined,      
      securityAnswer: undefined,      
      twoFaStatus: false ,      
      recoveryEmail: undefined,      
      recoveryPhone: undefined,      
      securityKey: undefined,      
      recoveryCodes: undefined,      
      displayName: undefined,      
      userRole: undefined,      
      linkedSocialAccounts: undefined,      
      registrationDate: undefined,      
      lastLoginTime: undefined,      
      accountExpiryDate: undefined,      
      accountStatus: false ,      
      riskAlert: undefined,      
      paymentMethod: undefined,      
      subscriptionPlan: undefined,      
      autoRenewalStatus: false ,      
      apiKey: undefined,      
      apiPermissions: undefined,      
      apiExpiryDate: undefined,      
      specialRequirements: undefined,      
      historyLog: undefined,      
      attachments: undefined,      
      manageUser: undefined,      
      manageMobile: undefined,      
      useUser: undefined,      
      useMobile: undefined,      
      deptId: undefined,      
      createdBy: undefined,      
      updatedBy: undefined,      
      createdAt: undefined,      
      updatedAt: undefined,      
      deletedAt: undefined,      
    },
    // 表单校验
    rules: {      
      id : [
          { required: true, message: "ID不能为空", trigger: "blur" }
      ],      
      platformName : [
          { required: true, message: "网站/应用名称不能为空", trigger: "blur" }
      ],      
      platformType : [
          { required: true, message: "平台类型不能为空", trigger: "blur" }
      ],      
      officialUrl : [
          { required: true, message: "官网网址不能为空", trigger: "blur" }
      ],      
      accountPurpose : [
          { required: true, message: "账号用途不能为空", trigger: "blur" }
      ],      
      username : [
          { required: true, message: "账号不能为空", trigger: "blur" }
      ],      
      loginEmail : [
          { required: true, message: "登录邮箱不能为空", trigger: "blur" }
      ],      
      loginPassword : [
          { required: true, message: "登录密码不能为空", trigger: "blur" }
      ],      
      twoFaStatus : [
          { required: true, message: "两步验证状态不能为空", trigger: "blur" }
      ],      
      displayName : [
          { required: true, message: "显示名称不能为空", trigger: "blur" }
      ],      
      registrationDate : [
          { required: true, message: "注册日期不能为空", trigger: "blur" }
      ],      
      accountStatus : [
          { required: true, message: "账号状态不能为空", trigger: "blur" }
      ],      
      autoRenewalStatus : [
          { required: true, message: "自动续费状态不能为空", trigger: "blur" }
      ],      
    }
  });
  const { isShowDialog,formData } = toRefs(state);
  // 打开弹窗
  const openDialog = (row?: CmAccountRecordInfoData) => {
    resetForm();
    if(row) {
      getCmAccountRecord(row.id!).then((res:any)=>{
        const data = res.data;        
        data.deptId = data.deptInfo?.deptName        
        data.createdBy = data.createdUser?.userNickname        
        data.updatedBy = data.updatedUser?.userNickname        
        state.formData = data;
      })
    }
    state.isShowDialog = true;
  };
  // 关闭弹窗
  const closeDialog = () => {
    state.isShowDialog = false;
  };
  defineExpose({
    openDialog,
  });
  // 取消
  const onCancel = () => {
    closeDialog();
  };
  const resetForm = ()=>{
    state.formData = {      
      id: undefined,      
      platformName: undefined,      
      platformType: undefined,      
      officialUrl: undefined,      
      accountPurpose: undefined,      
      registeredRegion: undefined,      
      username: undefined,      
      loginEmail: undefined,      
      boundPhoneNumber: undefined,      
      loginPassword: undefined,      
      passwordUpdateDate: undefined,      
      securityQuestion: undefined,      
      securityAnswer: undefined,      
      twoFaStatus: false ,      
      recoveryEmail: undefined,      
      recoveryPhone: undefined,      
      securityKey: undefined,      
      recoveryCodes: undefined,      
      displayName: undefined,      
      userRole: undefined,      
      linkedSocialAccounts: undefined,      
      registrationDate: undefined,      
      lastLoginTime: undefined,      
      accountExpiryDate: undefined,      
      accountStatus: false ,      
      riskAlert: undefined,      
      paymentMethod: undefined,      
      subscriptionPlan: undefined,      
      autoRenewalStatus: false ,      
      apiKey: undefined,      
      apiPermissions: undefined,      
      apiExpiryDate: undefined,      
      specialRequirements: undefined,      
      historyLog: undefined,      
      attachments: undefined,      
      manageUser: undefined,      
      manageMobile: undefined,      
      useUser: undefined,      
      useMobile: undefined,      
      deptId: undefined,      
      createdBy: undefined,      
      updatedBy: undefined,      
      createdAt: undefined,      
      updatedAt: undefined,      
      deletedAt: undefined,      
    }
  };  
</script>
<style scoped>  
  .cm-cmAccountRecord-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>