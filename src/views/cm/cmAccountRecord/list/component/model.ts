export interface CmAccountRecordTableColumns {    
    id:number;  // ID    
    platformName:string;  // 网站/应用名称    
    username:string;  // 账号    
    loginEmail:string;  // 登录邮箱    
    boundPhoneNumber:string;  // 绑定手机号    
    displayName:string;  // 显示名称    
    userRole:string;  // 用户角色    
    registrationDate:string;  // 注册日期    
    lastLoginTime:string;  // 最后登录时间    
    accountExpiryDate:string;  // 账号有效期    
    accountStatus:string;  // 账号状态    
    createdAt:string;  // 创建时间    
}


export interface CmAccountRecordInfoData {    
    id:number|undefined;        // ID    
    platformName:string|undefined; // 网站/应用名称    
    platformType:string|undefined; // 平台类型    
    officialUrl:string|undefined; // 官网网址    
    accountPurpose:string|undefined; // 账号用途    
    registeredRegion:string|undefined; // 注册国家/地区    
    username:string|undefined; // 账号    
    loginEmail:string|undefined; // 登录邮箱    
    boundPhoneNumber:string|undefined; // 绑定手机号    
    loginPassword:string|undefined; // 登录密码    
    passwordUpdateDate:string|undefined; // 密码更新时间    
    securityQuestion:string|undefined; // 密码提示问题    
    securityAnswer:string|undefined; // 密码提示答案    
    twoFaStatus:string|undefined; // 两步验证状态    
    recoveryEmail:string|undefined; // 备用验证邮箱    
    recoveryPhone:string|undefined; // 备用验证手机号    
    securityKey:string|undefined; // 安全密钥标识    
    recoveryCodes:string|undefined; // 账号恢复码    
    displayName:string|undefined; // 显示名称    
    userRole:string|undefined; // 用户角色    
    linkedSocialAccounts:string|undefined; // 绑定社交账号    
    registrationDate:string|undefined; // 注册日期    
    lastLoginTime:string|undefined; // 最后登录时间    
    accountExpiryDate:string|undefined; // 账号有效期    
    accountStatus:string|undefined; // 账号状态    
    riskAlert:string|undefined; // 风险提示    
    paymentMethod:string|undefined; // 绑定支付方式    
    subscriptionPlan:string|undefined; // 订阅计划    
    autoRenewalStatus:string|undefined; // 自动续费状态    
    apiKey:string|undefined; // API密钥    
    apiPermissions:string|undefined; // API权限范围    
    apiExpiryDate:string|undefined; // API密钥有效期    
    specialRequirements:string|undefined; // 特殊要求    
    historyLog:string|undefined; // 历史记录    
    attachments:string|undefined; // 文件附件路径    
    manageUser:string|undefined; // 管理人    
    manageMobile:string|undefined; // 管理人手机号    
    useUser:string|undefined; // 使用人    
    useMobile:string|undefined; // 使用人手机号    
    deptId:number|undefined; // 创建部门    
    createdBy:number|undefined; // 创建人    
    updatedBy:number|undefined; // 修改人    
    createdAt:string|undefined; // 创建时间    
    updatedAt:string|undefined; // 修改时间    
    deletedAt:string|undefined; // 删除时间    
}


export interface CmAccountRecordTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmAccountRecordTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            platformName: string|undefined;            
            platformType: string|undefined;            
            officialUrl: string|undefined;            
            accountPurpose: string|undefined;            
            username: string|undefined;            
            loginEmail: string|undefined;            
            boundPhoneNumber: string|undefined;            
            recoveryPhone: string|undefined;            
            accountExpiryDate: string[];            
            accountStatus: string|undefined;            
            manageUser: string|undefined;            
            manageMobile: string|undefined;            
            useUser: string|undefined;            
            useMobile: string|undefined;            
            deptId: number|undefined;            
            createdBy: number|undefined;            
            createdAt: string[];            
            dateRange: string[];
        };
    };
}


export interface CmAccountRecordEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmAccountRecordInfoData;
    rules: object;
}