<template>
  <div class="cm-cmAccountRecord-container">
    <el-card shadow="hover">
        <div class="cm-cmAccountRecord-search mb15">
            <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
            <el-row>                
                <el-col :span="8" class="colBlock">
                  <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableData.param.id"
                        placeholder="请输入ID"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" class="colBlock">
                  <el-form-item label="网站/应用名称" prop="platformName">
                    <el-input
                        v-model="tableData.param.platformName"
                        placeholder="请输入网站/应用名称"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="!showAll ? 'colBlock' : 'colNone'">
                  <el-form-item>
                    <el-button type="primary"  @click="cmAccountRecordList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    <el-button type="primary" link  @click="toggleSearch">
                      {{ word }}
                      <el-icon v-show="showAll"><ele-ArrowUp/></el-icon>
                      <el-icon v-show="!showAll"><ele-ArrowDown /></el-icon>
                    </el-button>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="平台类型" prop="platformType">
                    <el-select filterable v-model="tableData.param.platformType" placeholder="请选择平台类型" clearable style="width:200px;">
                        <el-option
                            v-for="dict in cm_platform_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="官网网址" prop="officialUrl">
                    <el-input
                        v-model="tableData.param.officialUrl"
                        placeholder="请输入官网网址"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="账号用途" prop="accountPurpose">
                    <el-select filterable v-model="tableData.param.accountPurpose" placeholder="请选择账号用途" clearable style="width:200px;">
                        <el-option
                            v-for="dict in cm_account_purpose"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="账号" prop="username">
                    <el-input
                        v-model="tableData.param.username"
                        placeholder="请输入账号"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="登录邮箱" prop="loginEmail">
                    <el-input
                        v-model="tableData.param.loginEmail"
                        placeholder="请输入登录邮箱"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="绑定手机号" prop="boundPhoneNumber">
                    <el-input
                        v-model="tableData.param.boundPhoneNumber"
                        placeholder="请输入绑定手机号"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="备用验证手机号" prop="recoveryPhone">
                    <el-input
                        v-model="tableData.param.recoveryPhone"
                        placeholder="请输入备用验证手机号"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="账号有效期" prop="accountExpiryDate">
                    <el-date-picker
                        clearable  style="width: 200px"
                        v-model="tableData.param.accountExpiryDate"
                        value-format="YYYY-MM-DD"                    
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"                    
                     ></el-date-picker>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="账号状态" prop="accountStatus">
                    <el-select filterable v-model="tableData.param.accountStatus" placeholder="请选择账号状态" clearable style="width:200px;">
                        <el-option
                            v-for="dict in cm_account_status"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="管理人" prop="manageUser">
                    <el-input
                        v-model="tableData.param.manageUser"
                        placeholder="请输入管理人"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="管理人手机号" prop="manageMobile">
                    <el-input
                        v-model="tableData.param.manageMobile"
                        placeholder="请输入管理人手机号"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="使用人" prop="useUser">
                    <el-input
                        v-model="tableData.param.useUser"
                        placeholder="请输入使用人"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="使用人手机号" prop="useMobile">
                    <el-input
                        v-model="tableData.param.useMobile"
                        placeholder="请输入使用人手机号"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="创建部门" prop="deptId">
                    <el-input
                        v-model="tableData.param.deptId"
                        placeholder="请输入创建部门"
                        clearable                        
                        @keyup.enter.native="cmAccountRecordList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker
                        clearable  style="width: 200px"
                        v-model="tableData.param.createdAt"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"                    
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"                    
                    ></el-date-picker>
                  </el-form-item>
                </el-col>            
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item>
                    <el-button type="primary"  @click="cmAccountRecordList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    <el-button type="primary" link  @click="toggleSearch">
                        {{ word }}
                        <el-icon v-show="showAll"><ele-ArrowUp/></el-icon>
                        <el-icon v-show="!showAll"><ele-ArrowDown /></el-icon>
                    </el-button>
                  </el-form-item>
                </el-col>            
              </el-row>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-auth="'api/v1/cm/cmAccountRecord/add'"
                ><el-icon><ele-Plus /></el-icon>新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  :disabled="single"
                  @click="handleUpdate(null)"
                  v-auth="'api/v1/cm/cmAccountRecord/edit'"
                ><el-icon><ele-Edit /></el-icon>修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  :disabled="multiple"
                  @click="handleDelete(null)"
                  v-auth="'api/v1/cm/cmAccountRecord/delete'"
                ><el-icon><ele-Delete /></el-icon>删除</el-button>
              </el-col>             
             <el-col :span="1.5">
                <el-button
                        type="warning"
                        @click="handleExport()"
                        v-auth="'api/v1/cm/cmAccountRecord/export'"
                ><el-icon><ele-Download /></el-icon>导出Excel</el-button>
             </el-col>            
            </el-row>
        </div>
        <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />          
          <el-table-column label="ID" align="center" prop="id"
            min-width="150px"            
             />          
          <el-table-column label="网站/应用名称" align="center" prop="platformName"
            min-width="150px"            
             />          
          <el-table-column label="账号" align="center" prop="username"
            min-width="150px"            
             />          
          <el-table-column label="登录邮箱" align="center" prop="loginEmail"
            min-width="150px"            
             />          
          <el-table-column label="绑定手机号" align="center" prop="boundPhoneNumber"
            min-width="150px"            
             />          
          <el-table-column label="显示名称" align="center" prop="displayName"
            min-width="150px"            
             />          
          <el-table-column label="用户角色" align="center" prop="userRole" :formatter="userRoleFormat"
            min-width="150px"            
             />          
          <el-table-column label="注册日期" align="center" prop="registrationDate"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.registrationDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>          
          <el-table-column label="最后登录时间" align="center" prop="lastLoginTime"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.lastLoginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>          
          <el-table-column label="账号有效期" align="center" prop="accountExpiryDate"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.accountExpiryDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>          
          <el-table-column label="账号状态" align="center" prop="accountStatus" :formatter="accountStatusFormat"
            min-width="150px"            
             />          
          <el-table-column label="创建时间" align="center" prop="createdAt"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>        
          <el-table-column label="操作" align="center" class-name="small-padding" min-width="200px" fixed="right">
            <template #default="scope">            
              <el-button
                type="primary"
                link
                @click="handleView(scope.row)"
                v-auth="'api/v1/cm/cmAccountRecord/get'"
              ><el-icon><ele-View /></el-icon>详情</el-button>              
              <el-button
                type="primary"
                link
                @click="handleUpdate(scope.row)"
                v-auth="'api/v1/cm/cmAccountRecord/edit'"
              ><el-icon><ele-EditPen /></el-icon>修改</el-button>
              <el-button
                type="primary"
                link
                @click="handleDelete(scope.row)"
                v-auth="'api/v1/cm/cmAccountRecord/delete'"
              ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="tableData.total>0"
            :total="tableData.total"
            v-model:page="tableData.param.pageNum"
            v-model:limit="tableData.param.pageSize"
            @pagination="cmAccountRecordList"
        />
    </el-card>
    <ApiV1CmCmAccountRecordEdit
       ref="editRef"       
       :platformTypeOptions="cm_platform_type"       
       :accountPurposeOptions="cm_account_purpose"       
       :twoFaStatusOptions="sys_yes_no"       
       :userRoleOptions="cm_user_role"       
       :accountStatusOptions="cm_account_status"       
       :autoRenewalStatusOptions="sys_yes_no"       
       @cmAccountRecordList="cmAccountRecordList"
    ></ApiV1CmCmAccountRecordEdit>
    <ApiV1CmCmAccountRecordDetail
      ref="detailRef"      
      :platformTypeOptions="cm_platform_type"      
      :accountPurposeOptions="cm_account_purpose"      
      :twoFaStatusOptions="sys_yes_no"      
      :userRoleOptions="cm_user_role"      
      :accountStatusOptions="cm_account_status"      
      :autoRenewalStatusOptions="sys_yes_no"      
      @cmAccountRecordList="cmAccountRecordList"
    ></ApiV1CmCmAccountRecordDetail>    
  </div>
</template>
<script setup lang="ts">
import {ItemOptions} from "/@/api/items";
import {toRefs, reactive, onMounted, ref, defineComponent, computed,getCurrentInstance,toRaw} from 'vue';
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus';
import {
    listCmAccountRecord,
    getCmAccountRecord,
    delCmAccountRecord,
    addCmAccountRecord,
    updateCmAccountRecord,    
} from "/@/api/cm/cmAccountRecord";
import {
    CmAccountRecordTableColumns,
    CmAccountRecordInfoData,
    CmAccountRecordTableDataState,    
} from "/@/views/cm/cmAccountRecord/list/component/model"
import ApiV1CmCmAccountRecordEdit from "/@/views/cm/cmAccountRecord/list/component/edit.vue"
import ApiV1CmCmAccountRecordDetail from "/@/views/cm/cmAccountRecord/list/component/detail.vue"
import {downLoadXml} from "/@/utils/zipdownload";
defineOptions({ name: "apiV1CmCmAccountRecordList"})
const {proxy} = <any>getCurrentInstance()
const loading = ref(false)
const queryRef = ref()
const editRef = ref();
const detailRef = ref();
// 是否显示所有搜索选项
const showAll =  ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple =ref(true)
const word = computed(()=>{
    if(showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
    } else {
        return "收起搜索";
    }
})
// 字典选项数据
const {    
    cm_platform_type,    
    cm_account_purpose,    
    sys_yes_no,    
    cm_user_role,    
    cm_account_status,    
} = proxy.useDict(    
    'cm_platform_type',    
    'cm_account_purpose',    
    'sys_yes_no',    
    'cm_user_role',    
    'cm_account_status',    
)
const state = reactive<CmAccountRecordTableDataState>({
    ids:[],
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,            
            id: undefined,            
            platformName: undefined,            
            platformType: undefined,            
            officialUrl: undefined,            
            accountPurpose: undefined,            
            username: undefined,            
            loginEmail: undefined,            
            boundPhoneNumber: undefined,            
            recoveryPhone: undefined,            
            accountExpiryDate: [],            
            accountStatus: undefined,            
            manageUser: undefined,            
            manageMobile: undefined,            
            useUser: undefined,            
            useMobile: undefined,            
            deptId: undefined,            
            createdBy: undefined,            
            createdAt: [],            
            dateRange: []
        },
    },
});
const { tableData } = toRefs(state);
// 页面加载时
onMounted(() => {
    initTableData();
});
// 初始化表格数据
const initTableData = () => {    
    cmAccountRecordList()
};
/** 重置按钮操作 */
const resetQuery = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    cmAccountRecordList()
};
// 获取列表数据
const cmAccountRecordList = ()=>{
  loading.value = true
  listCmAccountRecord(state.tableData.param).then((res:any)=>{
    let list = res.data.list??[];    
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false
  })
};
const toggleSearch = () => {
    showAll.value = !showAll.value;
}
// 平台类型字典翻译
const platformTypeFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(cm_platform_type.value, row.platformType);
}
// 账号用途字典翻译
const accountPurposeFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(cm_account_purpose.value, row.accountPurpose);
}
// 两步验证状态字典翻译
const twoFaStatusFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(sys_yes_no.value, row.twoFaStatus);
}
// 用户角色字典翻译
const userRoleFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(cm_user_role.value, row.userRole);
}
// 账号状态字典翻译
const accountStatusFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(cm_account_status.value, row.accountStatus);
}
// 自动续费状态字典翻译
const autoRenewalStatusFormat = (row:CmAccountRecordTableColumns) => {
    return proxy.selectDictLabel(sys_yes_no.value, row.autoRenewalStatus);
}
// 多选框选中数据
const handleSelectionChange = (selection:Array<CmAccountRecordInfoData>) => {
    state.ids = selection.map(item => item.id)
    single.value = selection.length!=1
    multiple.value = !selection.length
}
const handleAdd =  ()=>{
    editRef.value.openDialog()
}
const handleUpdate = (row: CmAccountRecordTableColumns|null) => {
    if(!row){
        row = state.tableData.data.find((item:CmAccountRecordTableColumns)=>{
            return item.id ===state.ids[0]
        }) as CmAccountRecordTableColumns
    }
    editRef.value.openDialog(toRaw(row));
};
const handleDelete = (row: CmAccountRecordTableColumns|null) => {
    let msg = '你确定要删除所选数据？';
    let id:number[] = [] ;
    if(row){
    msg = `此操作将永久删除数据，是否继续?`
    id = [row.id]
    }else{
    id = state.ids
    }
    if(id.length===0){
        ElMessage.error('请选择要删除的数据。');
        return
    }
    ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delCmAccountRecord(id).then(()=>{
                ElMessage.success('删除成功');
                cmAccountRecordList();
            })
        })
        .catch(() => {});
}
const handleView = (row:CmAccountRecordTableColumns)=>{
    detailRef.value.openDialog(toRaw(row));
}
//导出excel
const handleExport = ()=>{
    downLoadXml('/api/v1/cm/cmAccountRecord/export',state.tableData.param,'get')
}
</script>
<style lang="scss" scoped>
    .colBlock {
        display: block;
    }
    .colNone {
        display: none;
    }
    .ml-2{margin: 3px;}
</style>