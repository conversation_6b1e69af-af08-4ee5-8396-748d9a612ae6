export interface PluginSmsConfigTableColumns {    
    id:number;  // ID    
    smsType:string;  // 短信平台    
    remark:string;  // 备注    
    status:number;  // 状态    
}


export interface PluginSmsConfigInfoData {    
    id:number|undefined;        // ID    
    smsType:string|undefined; // 短信平台    
    remark:string|undefined; // 备注    
    status:boolean; // 状态    
    config:string|undefined; // 配置    
}


export interface PluginSmsConfigTableDataState {
    ids:any[];
    tableData: {
        data: Array<PluginSmsConfigTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            smsType: string|undefined;            
            status: number|undefined;            
            dateRange: string[];
        };
    };
}


export interface PluginSmsConfigEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:PluginSmsConfigInfoData;
    rules: object;
}