<template>
  <!-- 敏感词过滤详情抽屉 -->  
  <div class="keywordfilter-keywordFilter-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>敏感词过滤详情</h4>
      </template>
      <el-descriptions
              class="margin-top"
              :column="3"
              border
              style="margin: 8px;"
      >        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  主键ID
                </div>
              </template>
              {{ formData.id }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  过滤关键词
                </div>
              </template>
              {{ formData.keyword }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  分类
                </div>
              </template>
              {{ formData.category }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    匹配模式
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.matchMode, matchModeOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    处理动作
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.action, actionOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  替换文本
                </div>
              </template>
              {{ formData.replacement }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  风险等级
                </div>
              </template>
              {{ formData.riskLevel }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">              
                <template #label>
                  <div class="cell-item">
                    状态
                  </div>
                </template>
                {{ proxy.getOptionValue(formData.status, statusOptions,'value','label') }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                创建时间
              </div>
            </template>
            {{ proxy.parseTime(formData.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>        
      </el-descriptions>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listKeywordFilter,
    getKeywordFilter,
    delKeywordFilter,
    addKeywordFilter,
    updateKeywordFilter,    
  } from "/@/api/keywordfilter/keywordFilter";  
  import {
    KeywordFilterTableColumns,
    KeywordFilterInfoData,
    KeywordFilterTableDataState,
    KeywordFilterEditState
  } from "/@/views/keywordfilter/keywordFilter/list/component/model"
  defineOptions({ name: "ApiV1KeywordfilterKeywordFilterDetail"})  
  const props = defineProps({    
    matchModeOptions:{
      type:Array,
      default:()=>[]
    },    
    actionOptions:{
      type:Array,
      default:()=>[]
    },    
    statusOptions:{
      type:Array,
      default:()=>[]
    },    
  })  
  const {proxy} = <any>getCurrentInstance()
  const formRef = ref<HTMLElement | null>(null);
  const menuRef = ref();  
  const state = reactive<KeywordFilterEditState>({
    loading:false,
    isShowDialog: false,
    formData: {      
      id: undefined,      
      keyword: undefined,      
      category: undefined,      
      matchMode: false ,      
      action: false ,      
      replacement: undefined,      
      riskLevel: undefined,      
      status: false ,      
      createdAt: undefined,      
      updatedAt: undefined,      
    },
    // 表单校验
    rules: {      
      id : [
          { required: true, message: "主键ID不能为空", trigger: "blur" }
      ],      
      keyword : [
          { required: true, message: "过滤关键词不能为空", trigger: "blur" }
      ],      
      status : [
          { required: true, message: "状态不能为空", trigger: "blur" }
      ],      
    }
  });
  const { isShowDialog,formData } = toRefs(state);
  // 打开弹窗
  const openDialog = (row?: KeywordFilterInfoData) => {
    resetForm();
    if(row) {
      getKeywordFilter(row.id!).then((res:any)=>{
        const data = res.data;        
        state.formData = data;
      })
    }
    state.isShowDialog = true;
  };
  // 关闭弹窗
  const closeDialog = () => {
    state.isShowDialog = false;
  };
  defineExpose({
    openDialog,
  });
  // 取消
  const onCancel = () => {
    closeDialog();
  };
  const resetForm = ()=>{
    state.formData = {      
      id: undefined,      
      keyword: undefined,      
      category: undefined,      
      matchMode: false ,      
      action: false ,      
      replacement: undefined,      
      riskLevel: undefined,      
      status: false ,      
      createdAt: undefined,      
      updatedAt: undefined,      
    }
  };  
</script>
<style scoped>  
  .keywordfilter-keywordFilter-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>