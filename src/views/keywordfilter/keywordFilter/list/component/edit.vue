<template>
  <div class="keywordfilter-keywordFilter-edit">
    <!-- 添加或修改敏感词过滤对话框 -->
    <el-dialog v-model="isShowDialog" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.keywordfilter-keywordFilter-edit .el-dialog', '.keywordfilter-keywordFilter-edit .el-dialog__header']">{{(!formData.id || formData.id==0?'添加':'修改')+'敏感词过滤'}}</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="过滤关键词" prop="keyword">
          <el-input v-model="formData.keyword" placeholder="请输入过滤关键词" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="formData.category" placeholder="请输入分类" />
        </el-form-item>
        <el-form-item label="匹配模式" prop="matchMode">
          <el-radio-group v-model="formData.matchMode">
            <el-radio
              v-for="dict in matchModeOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理动作" prop="action">
          <el-radio-group v-model="formData.action">
            <el-radio
              v-for="dict in actionOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="替换文本" prop="replacement">
          <el-input v-model="formData.replacement" placeholder="请输入替换文本" />
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-input v-model="formData.riskLevel" placeholder="请输入风险等级(1-5)" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit" :disabled="loading">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, toRefs, ref,unref,getCurrentInstance,computed,watch } from 'vue';
import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
import {
  listKeywordFilter,
  getKeywordFilter,
  delKeywordFilter,
  addKeywordFilter,
  updateKeywordFilter,
} from "/@/api/keywordfilter/keywordFilter";
import {
  KeywordFilterTableColumns,
  KeywordFilterInfoData,
  KeywordFilterTableDataState,
  KeywordFilterEditState
} from "/@/views/keywordfilter/keywordFilter/list/component/model"
defineOptions({ name: "ApiV1KeywordfilterKeywordFilterEdit"})
const emit = defineEmits(['keywordFilterList'])
  const props = defineProps({
    matchModeOptions:{
      type:Array,
      default:()=>[]
    },
    actionOptions:{
      type:Array,
      default:()=>[]
    },
    statusOptions:{
      type:Array,
      default:()=>[]
    },
  })
const {proxy} = <any>getCurrentInstance()
const formRef = ref<HTMLElement | null>(null);
const menuRef = ref();

// 计算字典默认值
const getDefaultValue = (options: any[]) => {
  if (!options || options.length === 0) return '';
  // 查找标记为默认的选项
  const defaultOption = options.find(item => item.isDefault);
  if (defaultOption) return defaultOption.value;
  // 如果没有标记默认的，返回第一个选项的值
  return options[0]?.value || '';
};

const state = reactive<KeywordFilterEditState>({
  loading:false,
  isShowDialog: false,
  formData: {
    id: undefined,
    keyword: undefined,
    category: undefined,
    matchMode: '',
    action: '',
    replacement: undefined,
    riskLevel: undefined,
    status: '',
    createdAt: undefined,
    updatedAt: undefined,
  },
  // 表单校验
  rules: {
    id : [
        { required: true, message: "主键ID不能为空", trigger: "blur" }
    ],
    keyword : [
        { required: true, message: "过滤关键词不能为空", trigger: "blur" }
    ],
    status : [
        { required: true, message: "状态不能为空", trigger: "blur" }
    ],
  }
});
const { loading,isShowDialog,formData,rules } = toRefs(state);

// 监听字典数据变化，设置默认值
watch([() => props.matchModeOptions, () => props.actionOptions, () => props.statusOptions], () => {
  // 只在添加新记录时设置默认值（id为undefined或0时）
  if (!state.formData.id || state.formData.id === 0) {
    if (props.matchModeOptions.length > 0 && !state.formData.matchMode) {
      state.formData.matchMode = getDefaultValue(props.matchModeOptions);
    }
    if (props.actionOptions.length > 0 && !state.formData.action) {
      state.formData.action = getDefaultValue(props.actionOptions);
    }
    if (props.statusOptions.length > 0 && !state.formData.status) {
      state.formData.status = getDefaultValue(props.statusOptions);
    }
  }
}, { immediate: true });

// 打开弹窗
const openDialog = (row?: KeywordFilterInfoData) => {
  resetForm();
  if(row) {
    getKeywordFilter(row.id!).then((res:any)=>{
      const data = res.data;
      data.matchMode = ''+data.matchMode
      data.action = ''+data.action
      data.status = ''+data.status
      state.formData = data;
  })
}
  state.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.isShowDialog = false;
};
defineExpose({
  openDialog,
});
// 取消
const onCancel = () => {
  closeDialog();
};
// 提交
const onSubmit = () => {
  const formWrap = unref(formRef) as any;
  if (!formWrap) return;
  formWrap.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      if(!state.formData.id || state.formData.id===0){
        //添加
      addKeywordFilter(state.formData).then(()=>{
          ElMessage.success('添加成功');
          closeDialog(); // 关闭弹窗
          emit('keywordFilterList')
        }).finally(()=>{
          state.loading = false;
        })
      }else{
        //修改
      updateKeywordFilter(state.formData).then(()=>{
          ElMessage.success('修改成功');
          closeDialog(); // 关闭弹窗
          emit('keywordFilterList')
        }).finally(()=>{
          state.loading = false;
        })
      }
    }
  });
};
const resetForm = ()=>{
  state.formData = {
    id: undefined,
    keyword: undefined,
    category: undefined,
    matchMode: getDefaultValue(props.matchModeOptions),
    action: getDefaultValue(props.actionOptions),
    replacement: undefined,
    riskLevel: undefined,
    status: getDefaultValue(props.statusOptions),
    createdAt: undefined,
    updatedAt: undefined,
  }
};
</script>
<style scoped>
  .kv-label{margin-bottom: 15px;font-size: 14px;}
  .mini-btn i.el-icon{margin: unset;}
  .kv-row{margin-bottom: 12px;}
</style>