export interface KeywordFilterTableColumns {    
    id:number;  // 主键ID    
    keyword:string;  // 过滤关键词    
    category:string;  // 分类    
    matchMode:string;  // 匹配模式    
    action:string;  // 处理动作    
    replacement:string;  // 替换文本    
    riskLevel:number;  // 风险等级    
    status:number;  // 状态    
    createdAt:string;  // 创建时间    
}


export interface KeywordFilterInfoData {    
    id:number|undefined;        // 主键ID    
    keyword:string|undefined; // 过滤关键词    
    category:string|undefined; // 分类    
    matchMode:string|undefined; // 匹配模式    
    action:string|undefined; // 处理动作    
    replacement:string|undefined; // 替换文本    
    riskLevel:number|undefined; // 风险等级    
    status:number|undefined; // 状态    
    createdAt:string|undefined; // 创建时间    
    updatedAt:string|undefined; // 更新时间    
}


export interface KeywordFilterTableDataState {
    ids:any[];
    tableData: {
        data: Array<KeywordFilterTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            keyword: string|undefined;            
            category: string|undefined;            
            matchMode: string|undefined;            
            action: string|undefined;            
            riskLevel: number|undefined;            
            status: number|undefined;            
            createdAt: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface KeywordFilterEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:KeywordFilterInfoData;
    rules: object;
}