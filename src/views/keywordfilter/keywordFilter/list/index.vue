<template>
  <div class="keywordfilter-keywordFilter-container">
    <el-card shadow="hover">
        <div class="keywordfilter-keywordFilter-search mb15">
            <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
            <el-row>                
                <el-col :span="8" class="colBlock">
                  <el-form-item label="主键ID" prop="id">
                    <el-input
                        v-model="tableData.param.id"
                        placeholder="请输入主键ID"
                        clearable                        
                        @keyup.enter.native="keywordFilterList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" class="colBlock">
                  <el-form-item label="过滤关键词" prop="keyword">
                    <el-input
                        v-model="tableData.param.keyword"
                        placeholder="请输入过滤关键词"
                        clearable                        
                        @keyup.enter.native="keywordFilterList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="!showAll ? 'colBlock' : 'colNone'">
                  <el-form-item>
                    <el-button type="primary"  @click="keywordFilterList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    <el-button type="primary" link  @click="toggleSearch">
                      {{ word }}
                      <el-icon v-show="showAll"><ele-ArrowUp/></el-icon>
                      <el-icon v-show="!showAll"><ele-ArrowDown /></el-icon>
                    </el-button>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="分类" prop="category">
                    <el-select filterable v-model="tableData.param.category" placeholder="请选择分类" clearable style="width:200px;">
                        <el-option
                            v-for="dict in keyword_filter_category"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="匹配模式" prop="matchMode">
                    <el-select filterable v-model="tableData.param.matchMode" placeholder="请选择匹配模式" clearable style="width:200px;">
                        <el-option
                            v-for="dict in keyword_filter_match_mode"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="处理动作" prop="action">
                    <el-select filterable v-model="tableData.param.action" placeholder="请选择处理动作" clearable style="width:200px;">
                        <el-option
                            v-for="dict in keyword_filter_action"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="风险等级" prop="riskLevel">
                    <el-input
                        v-model="tableData.param.riskLevel"
                        placeholder="请输入风险等级"
                        clearable                        
                        @keyup.enter.native="keywordFilterList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="状态" prop="status">
                    <el-select filterable v-model="tableData.param.status" placeholder="请选择状态" clearable style="width:200px;">
                        <el-option
                            v-for="dict in sys_normal_disable"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker
                        clearable  style="width: 200px"
                        v-model="tableData.param.createdAt"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"                    
                        type="datetime"
                        placeholder="选择创建时间"                    
                    ></el-date-picker>
                  </el-form-item>
                </el-col>            
                <el-col :span="8" :class="showAll ? 'colBlock' : 'colNone'">
                  <el-form-item>
                    <el-button type="primary"  @click="keywordFilterList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    <el-button type="primary" link  @click="toggleSearch">
                        {{ word }}
                        <el-icon v-show="showAll"><ele-ArrowUp/></el-icon>
                        <el-icon v-show="!showAll"><ele-ArrowDown /></el-icon>
                    </el-button>
                  </el-form-item>
                </el-col>            
              </el-row>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-auth="'api/v1/keywordfilter/keywordFilter/add'"
                ><el-icon><ele-Plus /></el-icon>新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  :disabled="single"
                  @click="handleUpdate(null)"
                  v-auth="'api/v1/keywordfilter/keywordFilter/edit'"
                ><el-icon><ele-Edit /></el-icon>修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  :disabled="multiple"
                  @click="handleDelete(null)"
                  v-auth="'api/v1/keywordfilter/keywordFilter/delete'"
                ><el-icon><ele-Delete /></el-icon>删除</el-button>
              </el-col>             
             <el-col :span="1.5">
                <el-button
                        type="warning"
                        @click="handleExport()"
                        v-auth="'api/v1/keywordfilter/keywordFilter/export'"
                ><el-icon><ele-Download /></el-icon>导出Excel</el-button>
             </el-col>            
                <el-col :span="1.5">
                    <el-button
                            type="success"
                            @click="handleImport()"
                            v-auth="'api/v1/keywordfilter/keywordFilter/import'"
                    ><el-icon><ele-Upload /></el-icon>导入Excel</el-button>
                </el-col>            
            </el-row>
        </div>
        <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />          
          <el-table-column label="主键ID" align="center" prop="id"
            min-width="150px"            
             />          
          <el-table-column label="过滤关键词" align="center" prop="keyword"
            min-width="150px"            
             />          
          <el-table-column label="分类" align="center" prop="category" :formatter="categoryFormat"
            min-width="150px"            
             />          
          <el-table-column label="匹配模式" align="center" prop="matchMode" :formatter="matchModeFormat"
            min-width="150px"            
             />          
          <el-table-column label="处理动作" align="center" prop="action" :formatter="actionFormat"
            min-width="150px"            
             />          
          <el-table-column label="替换文本" align="center" prop="replacement"
            min-width="150px"            
             />          
          <el-table-column label="风险等级" align="center" prop="riskLevel"
            min-width="150px"            
             />          
          <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat"
            min-width="150px"            
             />          
          <el-table-column label="创建时间" align="center" prop="createdAt"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>        
          <el-table-column label="操作" align="center" class-name="small-padding" min-width="200px" fixed="right">
            <template #default="scope">            
              <el-button
                type="primary"
                link
                @click="handleView(scope.row)"
                v-auth="'api/v1/keywordfilter/keywordFilter/get'"
              ><el-icon><ele-View /></el-icon>详情</el-button>              
              <el-button
                type="primary"
                link
                @click="handleUpdate(scope.row)"
                v-auth="'api/v1/keywordfilter/keywordFilter/edit'"
              ><el-icon><ele-EditPen /></el-icon>修改</el-button>
              <el-button
                type="primary"
                link
                @click="handleDelete(scope.row)"
                v-auth="'api/v1/keywordfilter/keywordFilter/delete'"
              ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="tableData.total>0"
            :total="tableData.total"
            v-model:page="tableData.param.pageNum"
            v-model:limit="tableData.param.pageSize"
            @pagination="keywordFilterList"
        />
    </el-card>
    <ApiV1KeywordfilterKeywordFilterEdit
       ref="editRef"       
       :categoryOptions="keyword_filter_category"       
       :matchModeOptions="keyword_filter_match_mode"       
       :actionOptions="keyword_filter_action"       
       :statusOptions="sys_normal_disable"       
       @keywordFilterList="keywordFilterList"
    ></ApiV1KeywordfilterKeywordFilterEdit>
    <ApiV1KeywordfilterKeywordFilterDetail
      ref="detailRef"      
      :categoryOptions="keyword_filter_category"      
      :matchModeOptions="keyword_filter_match_mode"      
      :actionOptions="keyword_filter_action"      
      :statusOptions="sys_normal_disable"      
      @keywordFilterList="keywordFilterList"
    ></ApiV1KeywordfilterKeywordFilterDetail>    
    <loadExcel ref="loadExcelKeywordFilterRef" @getList="keywordFilterList"
               upUrl="api/v1/keywordfilter/keywordFilter/import"
               tplUrl="/api/v1/keywordfilter/keywordFilter/excelTemplate"></loadExcel>    
  </div>
</template>
<script setup lang="ts">
import {ItemOptions} from "/@/api/items";
import {toRefs, reactive, onMounted, ref, defineComponent, computed,getCurrentInstance,toRaw} from 'vue';
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus';
import {
    listKeywordFilter,
    getKeywordFilter,
    delKeywordFilter,
    addKeywordFilter,
    updateKeywordFilter,    
} from "/@/api/keywordfilter/keywordFilter";
import {
    KeywordFilterTableColumns,
    KeywordFilterInfoData,
    KeywordFilterTableDataState,    
} from "/@/views/keywordfilter/keywordFilter/list/component/model"
import ApiV1KeywordfilterKeywordFilterEdit from "/@/views/keywordfilter/keywordFilter/list/component/edit.vue"
import ApiV1KeywordfilterKeywordFilterDetail from "/@/views/keywordfilter/keywordFilter/list/component/detail.vue"
import {downLoadXml} from "/@/utils/zipdownload";
import loadExcel from "/@/components/loadExcel/index.vue"
defineOptions({ name: "apiV1KeywordfilterKeywordFilterList"})
const {proxy} = <any>getCurrentInstance()
const loading = ref(false)
const queryRef = ref()
const editRef = ref();
const detailRef = ref();
const loadExcelKeywordFilterRef = ref();
// 是否显示所有搜索选项
const showAll =  ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple =ref(true)
const word = computed(()=>{
    if(showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
    } else {
        return "收起搜索";
    }
})
// 字典选项数据
const {    
    keyword_filter_category,    
    keyword_filter_match_mode,    
    keyword_filter_action,    
    sys_normal_disable,    
} = proxy.useDict(    
    'keyword_filter_category',    
    'keyword_filter_match_mode',    
    'keyword_filter_action',    
    'sys_normal_disable',    
)
const state = reactive<KeywordFilterTableDataState>({
    ids:[],
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,            
            id: undefined,            
            keyword: undefined,            
            category: undefined,            
            matchMode: undefined,            
            action: undefined,            
            riskLevel: undefined,            
            status: undefined,            
            createdAt: undefined,            
            dateRange: []
        },
    },
});
const { tableData } = toRefs(state);
// 页面加载时
onMounted(() => {
    initTableData();
});
// 初始化表格数据
const initTableData = () => {    
    keywordFilterList()
};
/** 重置按钮操作 */
const resetQuery = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    keywordFilterList()
};
// 获取列表数据
const keywordFilterList = ()=>{
  loading.value = true
  listKeywordFilter(state.tableData.param).then((res:any)=>{
    let list = res.data.list??[];    
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false
  })
};
const toggleSearch = () => {
    showAll.value = !showAll.value;
}
// 分类字典翻译
const categoryFormat = (row:KeywordFilterTableColumns) => {
    return proxy.selectDictLabel(keyword_filter_category.value, row.category);
}
// 匹配模式字典翻译
const matchModeFormat = (row:KeywordFilterTableColumns) => {
    return proxy.selectDictLabel(keyword_filter_match_mode.value, row.matchMode);
}
// 处理动作字典翻译
const actionFormat = (row:KeywordFilterTableColumns) => {
    return proxy.selectDictLabel(keyword_filter_action.value, row.action);
}
// 状态字典翻译
const statusFormat = (row:KeywordFilterTableColumns) => {
    return proxy.selectDictLabel(sys_normal_disable.value, row.status);
}
// 多选框选中数据
const handleSelectionChange = (selection:Array<KeywordFilterInfoData>) => {
    state.ids = selection.map(item => item.id)
    single.value = selection.length!=1
    multiple.value = !selection.length
}
const handleAdd =  ()=>{
    editRef.value.openDialog()
}
const handleUpdate = (row: KeywordFilterTableColumns|null) => {
    if(!row){
        row = state.tableData.data.find((item:KeywordFilterTableColumns)=>{
            return item.id ===state.ids[0]
        }) as KeywordFilterTableColumns
    }
    editRef.value.openDialog(toRaw(row));
};
const handleDelete = (row: KeywordFilterTableColumns|null) => {
    let msg = '你确定要删除所选数据？';
    let id:number[] = [] ;
    if(row){
    msg = `此操作将永久删除数据，是否继续?`
    id = [row.id]
    }else{
    id = state.ids
    }
    if(id.length===0){
        ElMessage.error('请选择要删除的数据。');
        return
    }
    ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delKeywordFilter(id).then(()=>{
                ElMessage.success('删除成功');
                keywordFilterList();
            })
        })
        .catch(() => {});
}
const handleView = (row:KeywordFilterTableColumns)=>{
    detailRef.value.openDialog(toRaw(row));
}
//导出excel
const handleExport = ()=>{
    downLoadXml('/api/v1/keywordfilter/keywordFilter/export',state.tableData.param,'get')
}
const handleImport=()=>{
    loadExcelKeywordFilterRef.value.open()
}
</script>
<style lang="scss" scoped>
    .colBlock {
        display: block;
    }
    .colNone {
        display: none;
    }
    .ml-2{margin: 3px;}
</style>