export interface UcenterMembersTableColumns {
    id:number;  // ID
    nickname:string;  // 昵称
    sex:string;  // 性别
    email:string;  // 邮箱
    mobile:string;  // 手机号
    username:string;  // 用户名
    password:string;  // 密码
    salt:string;  // 密码盐
    uuid:string;  // UUID
    regType:string;  // 注册类型
    avatar:string;  // 头像
    createdAt:string;  // 创建时间
    wxOpenid:string;  // 微信openid
    isOpen:number;  // 是否激活
    status:number;  // 状态
    lastLoginIp:string;  // 最后登陆IP
    lastLoginAt:string;  // 最后登录日期
    qq:string;  // QQ号
    score:number;  // 积分
    level:number;  // 用户等级
    cover:string;  // 用户封面
    description:string;  // 个人说明
    isSecret:number;  // 是否保密
    isAdmin:number;  // 是否管理员
    linkedUcenterMembersUcenterLevel:LinkedUcenterMembersUcenterLevel;
}

export interface UcenterMembersInfoData {
    id:number|undefined;        // ID
    nickname:string|undefined; // 昵称
    sex:string; // 性别
    email:string|undefined; // 邮箱
    mobile:string|undefined; // 手机号
    username:string|undefined; // 用户名
    password:string|undefined; // 密码
    salt:string|undefined; // 密码盐
    uuid:string|undefined; // UUID
    regType:string|undefined; // 注册类型
    avatar:string|undefined; // 头像
    createdAt:string|undefined; // 创建时间
    updatedAt:string|undefined; // 更新时间
    deletedAt:string|undefined; // 删除时间
    wxOpenid:string|undefined; // 微信openid
    isOpen:boolean; // 是否激活
    status:boolean; // 状态
    lastLoginIp:string|undefined; // 最后登陆IP
    lastLoginAt:string|undefined; // 最后登录日期
    qq:string|undefined; // QQ号
    score:number|undefined; // 积分
    level:number|undefined; // 用户等级
    linkedLevel:LinkedUcenterMembersUcenterLevel; // 用户等级
    cover:string|undefined; // 用户封面
    description:string|undefined; // 个人说明
    isSecret:boolean; // 是否保密
    isAdmin:boolean; // 是否管理员
    linkedUcenterMembersUcenterLevel:LinkedUcenterMembersUcenterLevel;
}

export interface UcenterMembersAddData {
    id:number|undefined; // id
    nickname:string|undefined; // 昵称
    sex:string; // 性别
    deptId:number|0; // 部门ID
    email:string|undefined; // 邮箱
    mobile:string|undefined; // 手机号
    username:string|undefined; // 用户名
    password:string|undefined; // 密码
    wxOpenid:string|undefined; // 微信openid
    isOpen:boolean; // 是否激活
    level:string|undefined; // 用户等级
    isSecret:boolean; // 是否保密
}

export interface LinkedUcenterMembersUcenterLevel {
    id:number|undefined;    // 等级ID
    name:string|undefined;    // 等级名称
}

export interface UcenterMembersTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterMembersTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            nickname: string|undefined;
            mobile: string|undefined;
            username: string|undefined;
            regType: string|undefined;
            dateRange: string[];
        };
    };
}

export interface UcenterMembersResetPwd {
    loading:boolean;
    isShowDialog: boolean;
    formData:{
        id:number|undefined;
        newPassword:string|undefined;
    };
    rules: object;
}

export interface UcenterMembersEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterMembersAddData;
    rules: object;
}
