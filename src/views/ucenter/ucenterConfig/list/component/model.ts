export interface UcenterConfigTableColumns {    
    id:number;  // ID    
    name:string;  // 名称    
    value:string;  // 值    
    type:string;  // 值类型    
    tips:string;  // 提示信息    
}


export interface UcenterConfigInfoData {    
    id:number|undefined;        // ID    
    name:string|undefined; // 名称    
    value:string|undefined; // 值    
    type:string|undefined; // 值类型    
    tips:string|undefined; // 提示信息    
}


export interface UcenterConfigTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterConfigTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            dateRange: string[];
        };
    };
}


export interface UcenterConfigEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterConfigInfoData;
    rules: object;
}