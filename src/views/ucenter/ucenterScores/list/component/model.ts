export interface UcenterScoresTableColumns {
    id:number;  // ID
    memberId:number;  // 会员ID
    score:number;  // 积分
    dataSrc:string;  // 来源
    description:string;  // 描述
    type:number;  // 类型0:收入，1:支出
    createdAt:string;  // 创建日期
}


export interface UcenterScoresInfoData {
    id:number|undefined;        // ID
    memberId:number|undefined; // 会员ID
    score:number|undefined; // 积分
    dataSrc:string|undefined; // 来源
    description:string|undefined; // 描述
    type:number|undefined; // 类型0:收入，1:支出
    createdAt:string|undefined; // 创建日期
}


export interface UcenterScoresTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterScoresTableColumns>;
        total: number;
        loading: boolean;
        param: {
            username: string;
            pageNum: number;
            pageSize: number;
            dateRange: string[];
        };
    };
}


export interface UcenterScoresEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterScoresInfoData;
    rules: object;
}
