export interface UcenterMessageTableColumns {
    id:number;  // ID
    sender:number;  // 发送人member_id
    receiver:number;  // 收信人member_id
    content:string;  // 内容
    createdAt:string;  // 创建时间
    status:number;  // 状态:{0:未读,1:已读,2:已回复}
    pid:number;  // 父级消息ID
}


export interface UcenterMessageInfoData {
    id:number|undefined;        // ID
    sender:number|undefined; // 发送人member_id
    receiver:number|undefined; // 收信人member_id
    content:string|undefined; // 内容
    createdAt:string|undefined; // 创建时间
    updatedAt:string|undefined; // 更新时间
    status:boolean; // 状态:{0:未读,1:已读,2:已回复}
    pid:number|undefined; // 父级消息ID
}


export interface UcenterMessageTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterMessageTableColumns>;
        total: number;
        loading: boolean;
        param: {
            username: string;
            pageNum: number;
            pageSize: number;
            dateRange: string[];
        };
    };
}


export interface UcenterMessageEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterMessageInfoData;
    rules: object;
}
