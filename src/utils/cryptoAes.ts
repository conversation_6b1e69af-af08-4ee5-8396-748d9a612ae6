// @ts-ignore
import CryptoJS from "crypto-js";

const AES_KEY = "abcdefdes576ses6"; // 密钥, AES-128 需16个字符, AES-256 需要32个字符,要和后台的对应
const AES_IV = "abcdef0123456789"; // 密钥偏移量，16个字符

const key = CryptoJS.enc.Utf8.parse(AES_KEY);
const iv = CryptoJS.enc.Utf8.parse(AES_IV);

// 加密
export function encrypt(data: any): string {
    const srcs = CryptoJS.enc.Utf8.parse(data);
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
}

// 解密
export function decrypt(data: any): string {
    const decrypted = CryptoJS.AES.decrypt(data, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return CryptoJS.enc.Utf8.stringify(decrypted).toString();
}
